/* Enhanced Base Styles with Performance Optimizations */
body {
    font-family: 'Cairo', sans-serif;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Performance: Use GPU acceleration for smooth animations */
* {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid #0891b2;
    outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #0891b2;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

.section-card {
    background-color: #FFFFFF;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.05), 0 2px 4px -2px rgb(0 0 0 / 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-right: 4px solid transparent;
}

.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
    border-right-color: #0891b2; /* cyan-600 */
}

.tab-btn.active {
    background-color: #0891b2; /* cyan-600 */
    color: white;
    border-color: #0891b2;
}

/* Enhanced Interactive Tools Styles */

/* Tool Cards with Advanced Hover Effects */
.tool-card {
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    will-change: transform;
}

.tool-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.08);
}

.tool-card:focus-within {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(8, 145, 178, 0.15);
    border: 2px solid #0891b2;
}

/* Tool Button Enhancements */
.tool-btn {
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.025em;
    will-change: transform;
}

.tool-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tool-btn:hover::before {
    left: 100%;
}

/* Enhanced Modal Styles */
#tool-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

#tool-modal.show {
    opacity: 1;
}

#tool-modal.show #modal-dialog {
    transform: scale(1);
}

/* Custom Scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #0891b2 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #0891b2;
    border-radius: 4px;
    transition: background 0.3s;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #0e7490;
}

/* Tooltip Styles */
.tooltip-container [data-tooltip] {
    position: relative;
}

.tooltip-container [data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out forwards;
    pointer-events: none;
}

.tooltip-container [data-tooltip]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(2px);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.concept-node {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.concept-node:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.connection-line {
    pointer-events: none;
    opacity: 0.6;
}

.concept-map-demo {
    position: relative;
    width: 100%;
    height: 400px;
}

/* Code Editor Styles */
#code-editor {
    font-family: 'Courier New', monospace;
    line-height: 1.5;
    tab-size: 4;
}

#code-output {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

/* Canvas Styles */
canvas {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress Styles */
.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Animation for loading states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    /* Modal Adjustments */
    #tool-modal {
        padding: 1rem;
    }

    #tool-modal #modal-dialog {
        max-width: 100%;
        max-height: 95vh;
        margin: 0;
        border-radius: 12px;
    }

    #modal-content {
        padding: 1rem;
        max-height: calc(95vh - 100px);
    }

    /* Tool Cards Mobile Layout */
    .tool-card {
        padding: 1.5rem;
    }

    .tool-card:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .tool-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Concept Map Mobile */
    .concept-map-demo {
        height: 250px;
    }

    .concept-node {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    /* Navigation Mobile Improvements */
    .mobile-nav-link {
        font-size: 1.1rem;
        padding: 1rem;
    }

    /* Touch-friendly interactions */
    .tool-card, .tool-btn, button {
        min-height: 44px;
        min-width: 44px;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    .tool-card {
        padding: 1rem;
    }

    .tool-card h3 {
        font-size: 1.1rem;
    }

    .tool-card p {
        font-size: 0.85rem;
    }

    #modal-content {
        padding: 0.75rem;
    }
}

/* RTL (Right-to-Left) Enhancements */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .flex {
    flex-direction: row-reverse;
}

[dir="rtl"] .space-x-8 > * + * {
    margin-left: 0;
    margin-right: 2rem;
}

[dir="rtl"] .space-x-reverse > * + * {
    margin-left: 2rem;
    margin-right: 0;
}

[dir="rtl"] .border-r-4 {
    border-right: none;
    border-left: 4px solid;
}

[dir="rtl"] .ml-2 {
    margin-left: 0;
    margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .ml-3 {
    margin-left: 0;
    margin-right: 0.75rem;
}

/* RTL Tooltip Adjustments */
[dir="rtl"] .tooltip-container [data-tooltip]:hover::after {
    left: auto;
    right: 50%;
    transform: translateX(50%);
}

[dir="rtl"] .tooltip-container [data-tooltip]:hover::before {
    left: auto;
    right: 50%;
    transform: translateX(50%) translateY(2px);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .tool-card {
        border: 2px solid #000;
    }

    .tool-btn {
        border: 2px solid currentColor;
    }

    .section-card:hover {
        border-color: #0891b2;
    }
}

/* Entrance Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Staggered animation for tool cards */
.tool-card:nth-child(1) { animation-delay: 0.1s; }
.tool-card:nth-child(2) { animation-delay: 0.2s; }
.tool-card:nth-child(3) { animation-delay: 0.3s; }
.tool-card:nth-child(4) { animation-delay: 0.4s; }
.tool-card:nth-child(5) { animation-delay: 0.5s; }
.tool-card:nth-child(6) { animation-delay: 0.6s; }

/* Print Styles */
@media print {
    .skip-link,
    header,
    #tool-modal,
    .tool-btn,
    footer {
        display: none !important;
    }

    .tool-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .tool-card:hover {
        transform: none;
    }

    .tool-btn::before {
        display: none;
    }

    .animate-fade-in {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
    }

    /* Uncomment to enable dark mode
    body {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }

    .section-card {
        background-color: var(--bg-secondary);
        color: var(--text-primary);
    }
    */
}

/* Interactive elements hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0891b2;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0891b2;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
