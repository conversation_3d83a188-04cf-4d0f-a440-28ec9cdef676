body {
    font-family: 'Cairo', sans-serif;
    scroll-behavior: smooth;
}

.section-card {
    background-color: #FFFFFF;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.05), 0 2px 4px -2px rgb(0 0 0 / 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-right: 4px solid transparent;
}

.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
    border-right-color: #0891b2; /* cyan-600 */
}

.tab-btn.active {
    background-color: #0891b2; /* cyan-600 */
    color: white;
    border-color: #0891b2;
}
