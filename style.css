body {
    font-family: 'Cairo', sans-serif;
    scroll-behavior: smooth;
}

.section-card {
    background-color: #FFFFFF;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.05), 0 2px 4px -2px rgb(0 0 0 / 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-right: 4px solid transparent;
}

.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
    border-right-color: #0891b2; /* cyan-600 */
}

.tab-btn.active {
    background-color: #0891b2; /* cyan-600 */
    color: white;
    border-color: #0891b2;
}

/* Interactive Tools Styles */
#tool-modal {
    backdrop-filter: blur(4px);
}

.concept-node {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.concept-node:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.connection-line {
    pointer-events: none;
    opacity: 0.6;
}

.concept-map-demo {
    position: relative;
    width: 100%;
    height: 400px;
}

/* Code Editor Styles */
#code-editor {
    font-family: 'Courier New', monospace;
    line-height: 1.5;
    tab-size: 4;
}

#code-output {
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

/* Canvas Styles */
canvas {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress Styles */
.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Animation for loading states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive adjustments for tools */
@media (max-width: 768px) {
    #tool-modal .max-w-6xl {
        max-width: 95vw;
        margin: 1rem;
    }

    .concept-map-demo {
        height: 300px;
    }

    .concept-node {
        font-size: 0.8rem;
        padding: 6px;
    }
}

/* Interactive elements hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0891b2;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #0891b2;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
