<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vision par Ordinateur - Cours Interactif</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chosen Palette: Warm Neutrals with <PERSON><PERSON> Accent -->
    <!-- Application Structure Plan: 
        L'application est conçue comme une page unique (SPA) pour une expérience fluide et centralisée. La structure abandonne la linéarité du document source au profit d'une architecture thématique avec une barre de navigation fixe. Cela permet un accès non-linéaire et rapide à l'information clé. Le cœur de l'application est la section "Contenu de la matière", qui utilise un système d'onglets interactifs pour présenter les chapitres. Cette approche réduit la charge cognitive en se concentrant sur un sujet à la fois, ce qui est plus efficace pour l'apprentissage qu'un long défilement. Les données quantitatives (évaluation) sont visualisées pour une compréhension instantanée. Cette structure a été choisie pour transformer un syllabus passif en un outil d'exploration actif et convivial.
    -->
    <!-- Visualization & Content Choices:
        1.  Report Info: Contenu de la matière (9 chapitres). Goal: Organiser et rendre le contenu dense digestible. Viz/Presentation Method: Système d'onglets interactifs. Interaction: Clic sur un onglet pour afficher le contenu du chapitre correspondant. Justification: Empêche la surcharge d'informations et permet à l'utilisateur de contrôler son parcours d'apprentissage. Library/Method: Vanilla JS + HTML/Tailwind.
        2.  Report Info: Mode d'évaluation (60% examen, 40% évaluation continue). Goal: Informer rapidement sur la répartition des notes. Viz/Presentation Method: Graphique en anneau (Donut Chart). Interaction: Survol pour afficher les détails dans une infobulle. Justification: La visualisation de proportions est beaucoup plus rapide à interpréter que le texte seul. Library/Method: Chart.js sur Canvas.
        3.  Report Info: Objectifs et prérequis (listes à puces). Goal: Organiser et présenter clairement les points clés. Viz/Presentation Method: Cartes stylisées avec des icônes (caractères Unicode). Interaction: Organisation visuelle, avec un léger effet de survol pour l'engagement. Justification: Les cartes sont plus attrayantes visuellement et plus faciles à scanner qu'une simple liste, ce qui améliore la lisibilité. Library/Method: HTML/Tailwind CSS.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F8F7F4; /* Warm neutral background */
            color: #4B4B4B;
        }
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        .section-card {
            background-color: #FFFFFF;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.05), 0 2px 4px -2px rgb(0 0 0 / 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
        }
        .tab-btn.active {
            background-color: #0891b2; /* Cyan accent */
            color: white;
            border-color: #0891b2;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 350px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 400px;
        }
    </style>
</head>
<body class="smooth-scroll">

    <header class="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
        <nav class="container mx-auto px-6 py-3">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-cyan-800">Vision par Ordinateur</h1>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="text-gray-600 hover:text-cyan-600 transition">Accueil</a>
                    <a href="#objectifs" class="text-gray-600 hover:text-cyan-600 transition">Objectifs</a>
                    <a href="#contenu" class="text-gray-600 hover:text-cyan-600 transition">Contenu</a>
                    <a href="#evaluation" class="text-gray-600 hover:text-cyan-600 transition">Évaluation</a>
                    <a href="#outils" class="text-gray-600 hover:text-cyan-600 transition">Outils</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-btn" class="text-gray-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                </div>
            </div>
            <div id="mobile-menu" class="hidden md:hidden mt-3">
                <a href="#home" class="block py-2 text-gray-600 hover:text-cyan-600 transition">Accueil</a>
                <a href="#objectifs" class="block py-2 text-gray-600 hover:text-cyan-600 transition">Objectifs</a>
                <a href="#contenu" class="block py-2 text-gray-600 hover:text-cyan-600 transition">Contenu</a>
                <a href="#evaluation" class="block py-2 text-gray-600 hover:text-cyan-600 transition">Évaluation</a>
                <a href="#outils" class="block py-2 text-gray-600 hover:text-cyan-600 transition">Outils</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12">

        <section id="home" class="text-center mb-20">
            <h2 class="text-4xl font-bold text-cyan-900 mb-4">Master en Intelligence Artificielle</h2>
            <p class="text-lg text-gray-700 max-w-3xl mx-auto">
                Bienvenue dans le cours de Vision par Ordinateur. Cette plateforme interactive est conçue pour vous guider à travers les concepts fondamentaux, les techniques de pointe et les applications pratiques qui définissent ce domaine passionnant de l'intelligence artificielle.
            </p>
        </section>

        <section id="objectifs" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-900 mb-4">Objectifs de l'Enseignement</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">Ce cours vise à vous doter d'une compréhension approfondie de la vision artificielle, en couvrant les aspects théoriques et pratiques essentiels pour maîtriser le domaine.</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-2">Concepts Fondamentaux</h3>
                    <p class="text-gray-600">Introduire les bases de la perception visuelle artificielle, de la formation des images et de la photométrie.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🧠</div>
                    <h3 class="text-xl font-semibold mb-2">Méthodes Modernes</h3>
                    <p class="text-gray-600">Comprendre les approches classiques et modernes pour le traitement et l'analyse d'images et de vidéos.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-semibold mb-2">Deep Learning Avancé</h3>
                    <p class="text-gray-600">Maîtriser les techniques comme les CNNs, ResNet, et Vision Transformers pour des tâches complexes.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">💡</div>
                    <h3 class="text-xl font-semibold mb-2">Nouvelles Tendances</h3>
                    <p class="text-gray-600">Découvrir l'apprentissage auto-supervisé, les modèles de diffusion et les modèles multimodaux (vision + langage).</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🛠️</div>
                    <h3 class="text-xl font-semibold mb-2">Compétences Pratiques</h3>
                    <p class="text-gray-600">Développer une expertise avec des outils clés comme OpenCV, PyTorch, et TensorFlow.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold mb-2">Connaissances Préalables</h3>
                    <p class="text-gray-600">Algèbre linéaire, probabilités, statistiques et calcul différentiel sont recommandés pour réussir.</p>
                </div>
            </div>
        </section>

        <section id="contenu" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-900 mb-4">Contenu de la Matière</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">Le programme est structuré en plusieurs chapitres progressifs, des bases de la formation d'image aux applications les plus avancées. Utilisez les onglets ci-dessous pour explorer chaque section en détail.</p>
            </div>
            <div class="flex flex-wrap justify-center gap-2 mb-8" id="tabs-container">
            </div>
            <div id="tab-content-container" class="bg-white p-8 rounded-lg shadow-lg min-h-[250px] relative">
            </div>
        </section>

        <section id="evaluation" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-900 mb-4">Évaluation et Références</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">Votre performance sera évaluée à travers un examen final et un contrôle continu. Les ouvrages de référence listés ci-contre sont essentiels pour approfondir vos connaissances.</p>
            </div>
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="section-card p-6">
                    <h3 class="text-2xl font-semibold mb-4 text-center">Mode d'Évaluation</h3>
                    <div class="chart-container">
                        <canvas id="evaluationChart"></canvas>
                    </div>
                </div>
                <div class="section-card p-8">
                    <h3 class="text-2xl font-semibold mb-6">Références Bibliographiques</h3>
                    <ul class="space-y-6">
                        <li class="border-l-4 border-cyan-500 pl-4">
                            <p class="font-bold">Computer Vision: Algorithms and Applications (2nd ed.)</p>
                            <p class="text-sm text-gray-500">Szeliski, R. (2022). Springer.</p>
                        </li>
                        <li class="border-l-4 border-cyan-500 pl-4">
                            <p class="font-bold">Computer Vision: A Modern Approach (3rd ed.)</p>
                            <p class="text-sm text-gray-500">Forsyth, D. A. (2023). Pearson.</p>
                        </li>
                        <li class="border-l-4 border-cyan-500 pl-4">
                            <p class="font-bold">Deep Learning with Python (2nd ed.)</p>
                            <p class="text-sm text-gray-500">Chollet, F. (2021). Manning Publications.</p>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
        
        <section id="outils" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-900 mb-4">Outils et Frameworks</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">Nous utiliserons les bibliothèques et frameworks standards de l'industrie pour mettre en pratique les concepts étudiés et construire des applications de vision par ordinateur.</p>
            </div>
            <div class="flex flex-wrap justify-center items-center gap-8 md:gap-12">
                <div class="text-center text-gray-700 font-semibold">
                    <p class="text-5xl mb-2">📘</p>OpenCV
                </div>
                <div class="text-center text-gray-700 font-semibold">
                    <p class="text-5xl mb-2">🔥</p>PyTorch
                </div>
                <div class="text-center text-gray-700 font-semibold">
                    <p class="text-5xl mb-2">🧠</p>TensorFlow
                </div>
                <div class="text-center text-gray-700 font-semibold">
                    <p class="text-5xl mb-2">🤖</p>Detectron2
                </div>
            </div>
        </section>

        <section id="gemini" class="mb-20 section-card p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-cyan-900 mb-4">Mascotte IA ✨</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">
                    Avez-vous une question sur l'un des concepts du cours? Demandez à notre assistant IA, alimenté par Gemini, de vous aider. Il peut vous fournir des explications détaillées ou des clarifications sur les sujets.
                </p>
            </div>
            <div class="max-w-xl mx-auto">
                <div class="flex flex-col md:flex-row gap-4 mb-4">
                    <input type="text" id="prompt-input" class="flex-grow p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-cyan-500" placeholder="Demandez quelque chose sur le cours de Vision par Ordinateur...">
                    <button id="gemini-btn" class="bg-cyan-600 text-white font-semibold py-3 px-6 rounded-lg hover:bg-cyan-700 transition">
                        Demandez à Gemini ✨
                    </button>
                </div>
                <div id="gemini-response" class="bg-gray-100 p-4 rounded-lg min-h-[100px] text-gray-700">
                    <p>Votre réponse d'IA apparaîtra ici.</p>
                </div>
            </div>
        </section>

    </main>

    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-6 text-center">
            <p>&copy; 2025 Vision par Ordinateur. Conçu pour le Master en Intelligence Artificielle.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const menuBtn = document.getElementById('menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            menuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
            
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            const syllabusData = [
                { title: "Introduction", content: "Définition et histoire de la vision par ordinateur. Formation des images : géométrie, coordonnées, photométrie, numérisation." },
                { title: "Traitement d'Images", content: "Transformations ponctuelles, histogrammes. Filtres spatiaux pour le lissage et l'accentuation. Transformations de fréquence (Fourier)." },
                { title: "Détection de Caractéristiques", content: "Détection de contours (Canny) et de coins (Harris). Descripteurs de caractéristiques (SIFT, SURF, ORB) et mise en correspondance." },
                { title: "Bases du Deep Learning", content: "Réseaux de neurones artificiels (ANN). Rétropropagation (Backpropagation) et optimisation par descente de gradient. Fonctions d'activation et de perte." },
                { title: "Réseaux Convolutifs (CNN)", content: "Architecture des CNN : couches de convolution, de pooling, et entièrement connectées. Architectures célèbres (LeNet, AlexNet, ResNet). Applications : classification, segmentation (U-Net), détection d'objets (YOLO)." },
                { title: "Vision Transformers (ViT)", content: "Principe des Transformers appliqués aux images. Comparaison détaillée entre les approches CNN et ViT. Applications récentes en classification, segmentation, et détection." },
                { title: "Vision 3D et Vidéo", content: "Estimation du mouvement et du flux optique pour l'analyse de séquences. Vision stéréoscopique pour la perception de la profondeur. Analyse d'actions dans les flux vidéo." },
                { title: "Vision Multimodale", content: "Apprentissage auto-supervisé (SimCLR, MAE, MoCo). Modèles multimodaux (CLIP, BLIP-2) combinant vision et langage. Applications : recherche d'images par texte, VQA." },
                { title: "Applications Avancées", content: "Vision médicale pour l'analyse d'images (radiologie, imagerie biomédicale). Conduite autonome et détection de l'environnement. Réalité augmentée, biométrie, et robotique." },
            ];

            const tabsContainer = document.getElementById('tabs-container');
            const contentContainer = document.getElementById('tab-content-container');
            const promptInput = document.getElementById('prompt-input');
            const geminiBtn = document.getElementById('gemini-btn');
            const geminiResponse = document.getElementById('gemini-response');

            function updateTabContent(item) {
                const readBtn = `<button id="read-btn" class="bg-gray-200 text-gray-800 font-semibold py-2 px-4 rounded-lg hover:bg-gray-300 transition mr-2">Lire le contenu ✨</button>`;
                const quizBtn = `<button id="quiz-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">Créer un quiz ✨</button>`;
                const summaryBtn = `<button id="summary-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">Générer un résumé ✨</button>`;
                const glossaryBtn = `<button id="glossary-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">Extraire les termes clés ✨</button>`;
                const analogyBtn = `<button id="analogy-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">تبسيط بمقارنة ✨</button>`;
                const codeBtn = `<button id="code-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">Code example ✨</button>`;
                const mindmapBtn = `<button id="mindmap-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition mr-2">خريطة ذهنية ✨</button>`;
                const resourcesBtn = `<button id="resources-btn" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition">مصادر إضافية ✨</button>`;


                contentContainer.innerHTML = `
                    <h3 class='text-xl font-bold text-cyan-800 mb-4'>${item.title}</h3>
                    <p class='text-gray-700 leading-relaxed mb-6' id="chapter-text">${item.content}</p>
                    <div class="flex flex-wrap gap-2 mb-6">
                        ${readBtn}
                        ${quizBtn}
                        ${summaryBtn}
                        ${glossaryBtn}
                        ${analogyBtn}
                        ${codeBtn}
                        ${mindmapBtn}
                        ${resourcesBtn}
                    </div>
                    <div id="gemini-content-area" class="mt-8 pt-4 border-t border-gray-200 hidden"></div>
                    <div id="quiz-container" class="mt-8 pt-4 border-t border-gray-200 hidden"></div>
                `;
                
                document.getElementById('read-btn').addEventListener('click', () => {
                    readContent(item.content);
                });
                
                document.getElementById('quiz-btn').addEventListener('click', () => {
                    generateQuiz(item.content);
                });

                document.getElementById('summary-btn').addEventListener('click', () => {
                    generateSummary(item.content);
                });

                document.getElementById('glossary-btn').addEventListener('click', () => {
                    extractKeyTerms(item.content);
                });

                document.getElementById('analogy-btn').addEventListener('click', () => {
                    generateAnalogy(item.content);
                });

                document.getElementById('code-btn').addEventListener('click', () => {
                    generateCodeExample(item.title, item.content);
                });
                
                document.getElementById('mindmap-btn').addEventListener('click', () => {
                    generateMindMap(item.content);
                });

                document.getElementById('resources-btn').addEventListener('click', () => {
                    findRelatedResources(item.title);
                });
            }

            syllabusData.forEach((item, index) => {
                const tabButton = document.createElement('button');
                tabButton.textContent = item.title;
                tabButton.className = 'tab-btn px-4 py-2 text-sm font-semibold border-2 border-gray-300 rounded-full transition hover:bg-cyan-500 hover:text-white hover:border-cyan-500 focus:outline-none';
                if (index === 0) {
                    tabButton.classList.add('active');
                    updateTabContent(item);
                }
                tabButton.addEventListener('click', () => {
                    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                    tabButton.classList.add('active');
                    updateTabContent(item);
                });
                tabsContainer.appendChild(tabButton);
            });

            const ctx = document.getElementById('evaluationChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Examen Semestriel (60%)', 'Évaluation Continue (40%)'],
                    datasets: [{
                        data: [60, 40],
                        backgroundColor: ['#0891b2', '#f97316'],
                        borderColor: '#F8F7F4',
                        borderWidth: 4,
                        hoverOffset: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                font: {
                                    family: "'Inter', sans-serif",
                                    size: 14,
                                }
                            }
                        },
                        tooltip: {
                            bodyFont: {
                                family: "'Inter', sans-serif",
                            },
                            titleFont: {
                                family: "'Inter', sans-serif",
                            },
                            callbacks: {
                                label: function(context) {
                                    return ' ' + context.label;
                                }
                            }
                        }
                    }
                }
            });

            async function callGeminiApi(payload) {
                const apiKey = "AIzaSyBF9TW-VQhxMXRbEIt4pLX8LaWHRp2aKhk";
                const apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=" + apiKey;
                let response;
                let retries = 0;
                const maxRetries = 3;

                while (retries < maxRetries) {
                    try {
                        response = await fetch(apiUrl, {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(payload)
                        });

                        if (response.status === 429) {
                            const delay = Math.pow(2, retries) * 1000;
                            retries++;
                            await new Promise(resolve => setTimeout(resolve, delay));
                        } else {
                            break;
                        }
                    } catch (error) {
                        console.error("Fetch failed:", error);
                        throw error;
                    }
                }

                if (!response.ok) {
                    throw new Error(`API call failed with status: ${response.status}`);
                }

                return await response.json();
            }

            async function callGeminiSearchApi(payload) {
                const apiKey = "AIzaSyBF9TW-VQhxMXRbEIt4pLX8LaWHRp2aKhk";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;
                
                let response;
                let retries = 0;
                const maxRetries = 3;

                while (retries < maxRetries) {
                    try {
                        response = await fetch(apiUrl, {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(payload)
                        });

                        if (response.status === 429) {
                            const delay = Math.pow(2, retries) * 1000;
                            retries++;
                            await new Promise(resolve => setTimeout(resolve, delay));
                        } else {
                            break;
                        }
                    } catch (error) {
                        console.error("Fetch failed:", error);
                        throw error;
                    }
                }

                if (!response.ok) {
                    throw new Error(`API call failed with status: ${response.status}`);
                }

                return await response.json();
            }

            geminiBtn.addEventListener('click', async () => {
                const prompt = promptInput.value;
                if (!prompt) return;

                geminiResponse.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Chargement...</p></div>`;

                try {
                    const response = await callGeminiApi({
                        contents: [{
                            parts: [{ text: "En tant qu'assistant pour un cours de Vision par Ordinateur, réponds à cette question en français. Sois concis et utilise les termes techniques du domaine. Question: " + prompt }]
                        }]
                    });

                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    if (text) {
                        geminiResponse.innerText = text;
                    } else {
                        geminiResponse.innerText = "Désolé, je n'ai pas pu générer de réponse. Veuillez réessayer.";
                    }
                } catch (error) {
                    console.error('Error fetching from Gemini API:', error);
                    geminiResponse.innerText = "Une erreur est survenue lors de la communication avec l'IA.";
                }
            });

            async function readContent(textToRead) {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                const payload = {
                    contents: [{
                        parts: [{ text: textToRead }]
                    }],
                    generationConfig: {
                        responseModalities: ["AUDIO"],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: { voiceName: "Puck" }
                            }
                        }
                    },
                    model: "gemini-2.5-flash-preview-tts"
                };

                const apiKey = "AIzaSyBF9TW-VQhxMXRbEIt4pLX8LaWHRp2aKhk";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent?key=${apiKey}`;

                let audioResponse;
                let retries = 0;
                const maxRetries = 3;

                while (retries < maxRetries) {
                    try {
                        audioResponse = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });

                        if (audioResponse.status === 429) {
                            const delay = Math.pow(2, retries) * 1000;
                            retries++;
                            await new Promise(resolve => setTimeout(resolve, delay));
                        } else {
                            break;
                        }
                    } catch (error) {
                        console.error("Audio fetch failed:", error);
                        throw error;
                    }
                }
                
                if (!audioResponse.ok) {
                    console.error("TTS API call failed");
                    return;
                }

                const result = await audioResponse.json();
                const part = result?.candidates?.[0]?.content?.parts?.[0];
                const audioData = part?.inlineData?.data;
                const mimeType = part?.inlineData?.mimeType;

                if (audioData && mimeType && mimeType.startsWith("audio/")) {
                    const sampleRate = parseInt(mimeType.match(/rate=(\d+)/)[1], 10);
                    const pcmData = base64ToArrayBuffer(audioData);
                    const pcm16 = new Int16Array(pcmData);

                    const buffer = audioContext.createBuffer(1, pcm16.length, sampleRate);
                    const channelData = buffer.getChannelData(0);
                    for (let i = 0; i < pcm16.length; i++) {
                        channelData[i] = pcm16[i] / 32768;
                    }

                    const source = audioContext.createBufferSource();
                    source.buffer = buffer;
                    source.connect(audioContext.destination);
                    source.start();
                } else {
                    console.error("Invalid audio data received.");
                }
            }

            async function generateQuiz(chapterContent) {
                const quizContainer = document.getElementById('quiz-container');
                quizContainer.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Génération du quiz...</p></div>`;
                quizContainer.classList.remove('hidden');
                document.getElementById('gemini-content-area').classList.add('hidden');

                const quizPrompt = `Crée un quiz de 3 questions à choix multiples basé sur ce contenu: "${chapterContent}". Format la réponse en JSON comme un tableau d'objets. Chaque objet doit avoir une 'question' (chaîne de caractères), 'options' (tableau de chaînes de caractères), et 'réponse' (chaîne de caractères).`;

                const payload = {
                    contents: [{ parts: [{ text: quizPrompt }] }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "ARRAY",
                            items: {
                                type: "OBJECT",
                                properties: {
                                    "question": { "type": "STRING" },
                                    "options": {
                                        "type": "ARRAY",
                                        "items": { "type": "STRING" }
                                    },
                                    "réponse": { "type": "STRING" }
                                },
                                "propertyOrdering": ["question", "options", "réponse"]
                            }
                        }
                    }
                };
                
                try {
                    const response = await callGeminiApi(payload);
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    const quizData = JSON.parse(text);

                    renderQuiz(quizData);
                } catch (error) {
                    console.error('Error generating quiz:', error);
                    quizContainer.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la génération du quiz. Veuillez réessayer.</p>`;
                }
            }

            function renderQuiz(quizData) {
                const quizContainer = document.getElementById('quiz-container');
                quizContainer.innerHTML = '';
                
                quizData.forEach((q, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'mb-6 p-4 rounded-lg bg-gray-50';
                    questionDiv.innerHTML = `<p class="font-bold text-lg mb-2 text-gray-800">Question ${index + 1}: ${q.question}</p>`;
                    
                    q.options.forEach(option => {
                        const optionDiv = document.createElement('div');
                        optionDiv.className = 'flex items-center mb-2';
                        optionDiv.innerHTML = `<input type="radio" name="question-${index}" value="${option}" class="mr-2 accent-cyan-600 cursor-pointer" />
                                                <label class="text-gray-700">${option}</label>`;
                        questionDiv.appendChild(optionDiv);
                    });
                    
                    quizContainer.appendChild(questionDiv);
                });
                
                const checkBtn = document.createElement('button');
                checkBtn.textContent = 'Vérifier les réponses';
                checkBtn.className = 'mt-4 bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition';
                checkBtn.onclick = () => checkAnswers(quizData);
                quizContainer.appendChild(checkBtn);
            }

            function checkAnswers(quizData) {
                const quizContainer = document.getElementById('quiz-container');
                let score = 0;
                
                quizData.forEach((q, index) => {
                    const selectedOption = document.querySelector(`input[name="question-${index}"]:checked`);
                    const questionDiv = quizContainer.children[index];
                    const answerLabel = document.createElement('p');
                    
                    if (selectedOption) {
                        if (selectedOption.value === q.réponse) {
                            answerLabel.textContent = `Correct! La bonne réponse est : ${q.réponse}`;
                            answerLabel.className = 'mt-2 text-green-600 font-semibold';
                            score++;
                        } else {
                            answerLabel.textContent = `Incorrect. La bonne réponse est : ${q.réponse}`;
                            answerLabel.className = 'mt-2 text-red-600 font-semibold';
                        }
                    } else {
                        answerLabel.textContent = `Non répondu. La bonne réponse est : ${q.réponse}`;
                        answerLabel.className = 'mt-2 text-gray-500 font-semibold';
                    }
                    questionDiv.appendChild(answerLabel);
                });
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'mt-4 text-center text-xl font-bold';
                resultDiv.textContent = `Votre score : ${score} / ${quizData.length}`;
                quizContainer.appendChild(resultDiv);
                
                document.querySelector('#quiz-container button').disabled = true;
            }

            async function generateSummary(chapterContent) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Génération du résumé...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');

                const prompt = `Résume le texte suivant en 3 phrases, en français. Texe: "${chapterContent}"`;
                
                try {
                    const response = await callGeminiApi({
                        contents: [{ parts: [{ text: prompt }] }]
                    });
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    geminiContentArea.innerHTML = `<h3 class="font-bold text-lg mb-2">Résumé :</h3><p>${text}</p>`;
                } catch (error) {
                    console.error('Error generating summary:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la génération du résumé. Veuillez réessayer.</p>`;
                }
            }

            async function extractKeyTerms(chapterContent) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Extraction des termes clés...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');

                const prompt = `A partir de ce contenu, extrais 3 à 5 termes clés avec leurs définitions en français. Formate la réponse en JSON comme un tableau d'objets. Chaque objet doit avoir une 'term' (chaîne de caractères) et une 'definition' (chaîne de caractères). Contenu: "${chapterContent}"`;

                const payload = {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "ARRAY",
                            items: {
                                type: "OBJECT",
                                properties: {
                                    "term": { "type": "STRING" },
                                    "definition": { "type": "STRING" }
                                },
                                "propertyOrdering": ["term", "definition"]
                            }
                        }
                    }
                };

                try {
                    const response = await callGeminiApi(payload);
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    const termsData = JSON.parse(text);

                    let html = `<h3 class="font-bold text-lg mb-2">Termes Clés :</h3><ul class="list-disc list-inside space-y-2">`;
                    termsData.forEach(item => {
                        html += `<li><strong>${item.term}</strong> : ${item.definition}</li>`;
                    });
                    html += `</ul>`;
                    
                    geminiContentArea.innerHTML = html;
                } catch (error) {
                    console.error('Error extracting key terms:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de l'extraction des termes clés. Veuillez réessayer.</p>`;
                }
            }

            async function generateAnalogy(chapterContent) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Génération de l'analogie...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');

                const prompt = `Explique le texte suivant en utilisant une analogie simple et claire. Sois concis. Texte: "${chapterContent}"`;

                try {
                    const response = await callGeminiApi({
                        contents: [{ parts: [{ text: prompt }] }]
                    });
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    geminiContentArea.innerHTML = `<h3 class="font-bold text-lg mb-2">Analogie :</h3><p>${text}</p>`;
                } catch (error) {
                    console.error('Error generating analogy:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la génération de l'analogie. Veuillez réessayer.</p>`;
                }
            }

            async function generateCodeExample(chapterTitle, chapterContent) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Génération de l'exemple de code...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');

                const prompt = `Génère un exemple de code Python simple utilisant PyTorch pour illustrer le concept de "${chapterTitle}" basé sur ce texte: "${chapterContent}". Le code doit être complet, commenté et exécutable. Retourne uniquement le bloc de code.`;

                try {
                    const response = await callGeminiApi({
                        contents: [{ parts: [{ text: prompt }] }]
                    });
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    const codeHtml = `<h3 class="font-bold text-lg mb-2">Exemple de code :</h3><pre class="bg-gray-800 text-white p-4 rounded-lg overflow-x-auto"><code class="language-python">${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
                    geminiContentArea.innerHTML = codeHtml;
                } catch (error) {
                    console.error('Error generating code example:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la génération de l'exemple de code. Veuillez réessayer.</p>`;
                }
            }

            async function generateMindMap(chapterContent) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Génération de la carte mentale...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');
            
                const prompt = `Génère une structure de carte mentale hiérarchique en JSON à partir du texte suivant. Le JSON doit avoir un nœud racine unique ('label' et 'children') et chaque nœud enfant doit aussi avoir un 'label' et un tableau de 'children' pour la hiérarchie. Texte : "${chapterContent}"`;
            
                const payload = {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "OBJECT",
                            properties: {
                                "label": { "type": "STRING" },
                                "children": {
                                    "type": "ARRAY",
                                    "items": {
                                        "type": "OBJECT",
                                        "properties": {
                                            "label": { "type": "STRING" },
                                            "children": { "type": "ARRAY", "items": { "type": "OBJECT", "properties": { "label": { "type": "STRING" }, "children": { "type": "ARRAY", "items": { "type": "OBJECT", "properties": { "label": { "type": "STRING" } } } } } } }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };
            
                try {
                    const response = await callGeminiApi(payload);
                    const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
                    const mindMapData = JSON.parse(text);
                    
                    let html = `<h3 class="font-bold text-lg mb-4">Carte Mentale :</h3>`;
                    html += renderMindMap(mindMapData);
                    geminiContentArea.innerHTML = html;
                } catch (error) {
                    console.error('Error generating mind map:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la génération de la carte mentale. Veuillez réessayer.</p>`;
                }
            }
            
            function renderMindMap(node) {
                if (!node || !node.label) return '';
                let html = `<ul class="list-none space-y-2">`;
                html += `<li class="font-semibold text-gray-800">${node.label}</li>`;
                if (node.children && node.children.length > 0) {
                    html += `<ul class="list-none pl-6 border-l-2 border-gray-300 ml-2">`;
                    node.children.forEach(child => {
                        html += `<li>${renderMindMap(child)}</li>`;
                    });
                    html += `</ul>`;
                }
                html += `</ul>`;
                return html;
            }

            async function findRelatedResources(chapterTitle) {
                const geminiContentArea = document.getElementById('gemini-content-area');
                geminiContentArea.innerHTML = `<div class="flex items-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>Recherche de ressources...</p></div>`;
                geminiContentArea.classList.remove('hidden');
                document.getElementById('quiz-container').classList.add('hidden');
            
                const prompt = `Trouve trois ressources d'apprentissage en ligne de haute qualité, comme des articles de blog ou des tutoriels, sur le sujet suivant: "${chapterTitle}". Donne une brève description de chaque ressource.`;
            
                const payload = {
                    contents: [{ parts: [{ text: prompt }] }],
                    tools: [{ "google_search": {} }],
                };
            
                try {
                    const response = await callGeminiSearchApi(payload);
                    const candidate = response.candidates?.[0];
                    const text = candidate?.content?.parts?.[0]?.text;
                    
                    let html = `<h3 class="font-bold text-lg mb-2">Ressources en ligne :</h3><div class="space-y-4">`;
                    
                    if (text) {
                        html += `<p>${text}</p>`;
                    }
            
                    const groundingMetadata = candidate?.groundingMetadata;
                    const sources = groundingMetadata?.groundingAttributions
                        ?.map(attribution => ({
                            uri: attribution.web?.uri,
                            title: attribution.web?.title,
                        }))
                        .filter(source => source.uri && source.title);
            
                    if (sources && sources.length > 0) {
                        html += `<h4 class="font-bold mt-4">Sources :</h4><ul class="list-disc list-inside space-y-1">`;
                        sources.forEach(source => {
                            html += `<li><a href="${source.uri}" target="_blank" class="text-blue-600 hover:underline">${source.title}</a></li>`;
                        });
                        html += `</ul>`;
                    }
                    
                    html += `</div>`;
                    geminiContentArea.innerHTML = html;
            
                } catch (error) {
                    console.error('Error finding resources:', error);
                    geminiContentArea.innerHTML = `<p class="text-red-500">Une erreur est survenue lors de la recherche des ressources. Veuillez réessayer.</p>`;
                }
            }

            function base64ToArrayBuffer(base64) {
                const binaryString = atob(base64);
                const len = binaryString.length;
                const bytes = new Uint8Array(len);
                for (let i = 0; i < len; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                return bytes.buffer;
            }
        });
    </script>

</body>
</html>

