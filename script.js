// Additional utility functions for the interactive educational tools

// Utility function to generate random data for demonstrations
function generateRandomData(min, max, count) {
    return Array.from({ length: count }, () => Math.floor(Math.random() * (max - min + 1)) + min);
}

// Utility function to format numbers in Arabic
function formatNumberArabic(num) {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit => arabicNumerals[parseInt(digit)] || digit).join('');
}

// Utility function to create smooth animations
function animateValue(element, start, end, duration, callback) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = start + (end - start) * progress;
        if (callback) callback(current);

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// Enhanced error handling for API calls
function handleApiError(error, fallbackMessage) {
    console.error('API Error:', error);
    return fallbackMessage || 'حدث خطأ في الاتصال بالخدمة. يرجى المحاولة مرة أخرى.';
}

// Local storage utilities for progress tracking
const StorageUtils = {
    save: (key, data) => {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Storage save error:', error);
            return false;
        }
    },

    load: (key, defaultValue = null) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Storage load error:', error);
            return defaultValue;
        }
    },

    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Storage remove error:', error);
            return false;
        }
    }
};

// Performance monitoring for educational tools
const PerformanceMonitor = {
    startTime: null,

    start: (operation) => {
        PerformanceMonitor.startTime = performance.now();
        console.log(`Starting operation: ${operation}`);
    },

    end: (operation) => {
        if (PerformanceMonitor.startTime) {
            const duration = performance.now() - PerformanceMonitor.startTime;
            console.log(`Operation ${operation} completed in ${duration.toFixed(2)}ms`);
            PerformanceMonitor.startTime = null;
            return duration;
        }
        return 0;
    }
};

// Accessibility helpers
const AccessibilityUtils = {
    announceToScreenReader: (message) => {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;
        document.body.appendChild(announcement);

        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    },

    addKeyboardNavigation: (element, callback) => {
        element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                callback();
            }
        });
    }
};

console.log('Interactive educational tools utilities loaded successfully!');