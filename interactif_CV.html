<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرؤية الحاسوبية لطلاب الماستر</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

        :root {
            --primary-color: #4A90E2;
            --secondary-color: #50E3C2;
            --background-color: #F0F4F8;
            --card-background: #FFFFFF;
            --text-color: #333333;
            --heading-color: #1A237E;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition-speed: 0.3s;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            direction: rtl;
            line-height: 1.8;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        header {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: #fff;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 20px var(--shadow-color);
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        @media (min-width: 768px) {
            .main-content {
                grid-template-columns: 2fr 1fr;
            }
        }
        
        .section-card {
            background-color: var(--card-background);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: 0 4px 12px var(--shadow-color);
            transition: transform var(--transition-speed) ease-in-out;
            min-height: 250px;
        }

        .section-card:hover {
            transform: translateY(-5px);
        }

        .section-card h2 {
            color: var(--heading-color);
            border-bottom: 3px solid var(--secondary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }

        .side-panel {
            display: grid;
            gap: 2rem;
        }

        .chapter-list li, .tool-item, .evaluation-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: #F8F9FA;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color var(--transition-speed) ease;
        }

        .chapter-list li:hover, .tool-item:hover, .evaluation-item:hover {
            background-color: #E9ECEF;
        }

        .chapter-list li span, .evaluation-item span {
            flex-grow: 1;
        }
        
        .evaluation-item .percentage {
            font-weight: bold;
            color: var(--primary-color);
        }

        .ai-tool-section {
            display: none;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .ai-tool-section textarea {
            width: 100%;
            height: 150px;
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 1px solid #ccc;
        }

        .ai-tool-section button {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background-color var(--transition-speed) ease;
        }
        
        .ai-tool-section button:hover {
            background-color: #3770B3;
        }

        .ai-tool-section .result {
            padding: 1rem;
            border-radius: var(--border-radius);
            background-color: #E9F5FF;
            border: 1px solid #B0D8FF;
            width: 100%;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>

<div class="container">
    <header>
        <h1>مادة الرؤية الحاسوبية</h1>
        <p>محتوى تفاعلي لطلاب السنة الأولى ماستر</p>
    </header>

    <div class="main-content">
        <section class="section-card" id="main-content-display">
            <h2>مقدمة</h2>
            <p>الرؤية الحاسوبية هي مجال علمي يهدف إلى تمكين أجهزة الحاسوب من فهم وتفسير الصور ومقاطع الفيديو بنفس الطريقة التي يفهمها بها البشر. يشمل هذا المجال مجموعة واسعة من المهام، بدءًا من التعرف على الكائنات البسيطة وصولاً إلى فهم المشهد بالكامل. ستتعرف في هذا المساق على المفاهيم الأساسية، الخوارزميات، والتطبيقات الحديثة التي تشكل حجر الزاوية في الذكاء الاصطناعي.</p>
            <p>سيركز المساق على منهجية عملية، حيث سيتم دمج الشرح النظري مع أمثلة عملية باستخدام مكتبات شهيرة مثل OpenCV و TensorFlow. ستتعلم كيفية بناء مشاريع رؤية حاسوبية من الصفر، مما يجعلك جاهزاً لمواجهة التحديات في المجال الأكاديمي والمهني.</p>
            <p>
                <br>
                <span style="color: var(--primary-color);">استخدم الأدوات التفاعلية في الجانب الأيمن لاستكشاف المادة بشكل أعمق.</span>
            </p>
        </section>

        <aside class="side-panel">
            <section class="section-card">
                <h2>فصول المادة</h2>
                <ul class="chapter-list" id="chapter-list">
                    <li data-chapter="intro">
                        <span>1. مقدمة في الرؤية الحاسوبية</span>
                    </li>
                    <li data-chapter="basics">
                        <span>2. أساسيات معالجة الصور</span>
                    </li>
                    <li data-chapter="features">
                        <span>3. استخراج الميزات</span>
                    </li>
                    <li data-chapter="classification">
                        <span>4. تصنيف الصور والكائنات</span>
                    </li>
                    <li data-chapter="deep-learning">
                        <span>5. الرؤية الحاسوبية والتعلم العميق</span>
                    </li>
                    <li data-chapter="applications">
                        <span>6. تطبيقات متقدمة</span>
                    </li>
                </ul>
            </section>

            <section class="section-card">
                <h2>توزيع التقييم</h2>
                <ul class="evaluation-list">
                    <li class="evaluation-item">
                        <span>مشاريع عملية</span>
                        <span class="percentage">40%</span>
                    </li>
                    <li class="evaluation-item">
                        <span>اختبارات قصيرة</span>
                        <span class="percentage">20%</span>
                    </li>
                    <li class="evaluation-item">
                        <span>مشروع نهائي</span>
                        <span class="percentage">40%</span>
                    </li>
                </ul>
            </section>
            
            <section class="section-card">
                <h2>أدوات تفاعلية (AI)</h2>
                <div class="ai-tool-section" id="deep-dive-tool">
                    <p>أداة للتعمق: أدخل مفهوماً للحصول على شرح موسع</p>
                    <textarea id="deep-dive-input" placeholder="مثال: الشبكات العصبية الالتفافية"></textarea>
                    <button onclick="handleTool('deep-dive')">اشرح لي</button>
                    <div class="result" id="deep-dive-result"></div>
                </div>
                <div class="ai-tool-section" id="example-tool">
                    <p>أداة الأمثلة: أدخل مفهوماً للحصول على أمثلة تطبيقية</p>
                    <textarea id="example-input" placeholder="مثال: كشف الوجوه"></textarea>
                    <button onclick="handleTool('example')">أعطني أمثلة</button>
                    <div class="result" id="example-result"></div>
                </div>
                <ul class="tool-list">
                    <li class="tool-item" data-tool="deep-dive">
                        <span>أداة التعمق في المفاهيم</span>
                    </li>
                    <li class="tool-item" data-tool="example">
                        <span>أداة الأمثلة التطبيقية</span>
                    </li>
                </ul>
            </section>
        </aside>
    </div>
</div>

<script>
    const chapters = {
        'intro': {
            title: 'مقدمة في الرؤية الحاسوبية',
            content: `
                <p><strong>ما هي الرؤية الحاسوبية؟</strong></p>
                <p>تُعرف الرؤية الحاسوبية بأنها فرع من فروع الذكاء الاصطناعي يهدف إلى تمكين أجهزة الحاسوب من استخلاص معلومات ذات معنى من الصور الرقمية أو مقاطع الفيديو أو غيرها من المدخلات المرئية ومعالجتها. يتضمن ذلك مهام مثل التعرف على الكائنات، وتجزئة الصور (Image Segmentation)، وتتبع الحركة، واستعادة البُعد الثلاثي.</p>
                <p><strong>تاريخ وتطور المجال:</strong></p>
                <p>بدأ المجال في ستينيات القرن الماضي بمحاولات بسيطة لفهم الصور، وتطور بشكل كبير مع ظهور خوارزميات التعلم الآلي والتعلم العميق. شهدت السنوات الأخيرة طفرة هائلة بفضل توافر البيانات الضخمة (Big Data) وقوة المعالجة الحاسوبية.</p>
            `
        },
        'basics': {
            title: 'أساسيات معالجة الصور',
            content: `
                <p><strong>الصور كبيانات:</strong></p>
                <p>تُعتبر الصورة الرقمية مصفوفة من الأرقام (pixels)، حيث يمثل كل رقم شدة لونية معينة. تختلف هذه المصفوفات بناءً على نوع الصورة (أحادية اللون، RGB). يتم تطبيق العديد من العمليات عليها لتحسينها أو تجهيزها للتحليل.</p>
                <p><strong>العمليات الأساسية:</strong></p>
                <ul>
                    <li><strong>الترشيح (Filtering):</strong> يهدف إلى إزالة التشويش أو تحسين حواف الصورة. من الأمثلة الشائعة: مرشحات Gaussian و Median.</li>
                    <li><strong>العتبة (Thresholding):</strong> عملية تحويل صورة ملونة أو رمادية إلى صورة ثنائية (أبيض وأسود) بناءً على عتبة معينة.</li>
                    <li><strong>تحويل الألوان:</strong> تحويل الصورة من نظام لوني إلى آخر (مثال: RGB إلى Grayscale).</li>
                </ul>
            `
        },
        'features': {
            title: 'استخراج الميزات',
            content: `
                <p><strong>ما هي الميزات؟</strong></p>
                <p>الميزات (Features) هي نقاط أو مناطق مهمة في الصورة تحمل معلومات فريدة تساعد في تمييز الكائنات أو الأماكن. يمكن أن تكون هذه الميزات عبارة عن حواف، أو زوايا، أو مناطق ذات نسيج مميز.</p>
                <p><strong>الخوارزميات الشائعة:</strong></p>
                <ul>
                    <li><strong>SIFT (Scale-Invariant Feature Transform):</strong> خوارزمية قوية تستخرج ميزات مقاومة للتغير في الحجم والدوران.</li>
                    <li><strong>HOG (Histogram of Oriented Gradients):</strong> تُستخدم بشكل واسع في كشف الأشخاص من خلال وصف شكلهم عبر اتجاهات التدرجات اللونية.</li>
                    <li><strong>ORB (Oriented FAST and Rotated BRIEF):</strong> بديل سريع وفعال لخوارزميات SIFT و SURF.</li>
                </ul>
            `
        },
        'classification': {
            title: 'تصنيف الصور والكائنات',
            content: `
                <p><strong>تصنيف الصور (Image Classification):</strong></p>
                <p>هي مهمة أساسية في الرؤية الحاسوبية تهدف إلى تعيين فئة (أو علامة) واحدة للصورة بأكملها. مثال: تصنيف صورة ما إذا كانت تحتوي على "قطة" أو "كلب" أو "طائر".</p>
                <p><strong>كشف الكائنات (Object Detection):</strong></p>
                <p>تختلف عن التصنيف بأنها لا تحدد فئة الصورة ككل فقط، بل تحدد موقع الكائن داخل الصورة وتصنفه. يتم ذلك عن طريق رسم مربعات إحاطة (Bounding Boxes) حول كل كائن.</p>
            `
        },
        'deep-learning': {
            title: 'الرؤية الحاسوبية والتعلم العميق',
            content: `
                <p><strong>الشبكات العصبية الالتفافية (CNNs):</strong></p>
                <p>تُعتبر CNNs العمود الفقري للرؤية الحاسوبية الحديثة. هي نوع من الشبكات العصبية مصمم خصيصاً لمعالجة البيانات الهيكلية مثل الصور. تتكون من طبقات التفافية (Convolutional) وطبقات تجميع (Pooling) لتعلم الميزات بشكل هرمي.</p>
                <p><strong>النماذج الشهيرة:</strong></p>
                <ul>
                    <li><strong>AlexNet, VGGNet:</strong> من أوائل النماذج التي أظهرت قوة CNNs في مسابقات تصنيف الصور.</li>
                    <li><strong>ResNet, Inception:</strong> نماذج أحدث ساهمت في تحسين الأداء بشكل كبير من خلال هياكل معمارية مبتكرة.</li>
                </ul>
            `
        },
        'applications': {
            title: 'تطبيقات متقدمة',
            content: `
                <p><strong>تجزئة الصور (Image Segmentation):</strong></p>
                <p>تهدف إلى تقسيم الصورة إلى مناطق أو شرائح (Segments) بحيث تكون كل شريحة تمثل كائناً أو جزءاً من كائن. يتم تصنيف كل بكسل في الصورة.</p>
                <p><strong>تتبع الكائنات (Object Tracking):</strong></p>
                <p>هي عملية تحديد موقع كائن متحرك في سلسلة من الإطارات المتتابعة (فيديو). تُستخدم في أنظمة المراقبة، والمركبات ذاتية القيادة، والروبوتات.</p>
                <p><strong>الواقع المعزز (Augmented Reality):</strong></p>
                <p>تعتمد على الرؤية الحاسوبية لتحليل المشهد الواقعي وإضافة معلومات أو كائنات افتراضية إليه. مثال: تطبيقات فلاتر الوجه.</p>
            `
        }
    };

    const toolsData = {
        'deep-dive': {
            endpoint: 'gemini-2.5-flash-preview-05-20',
            promptPrefix: 'أعطني شرحاً تفصيلياً ومبسّطاً للمفهوم التالي في سياق الرؤية الحاسوبية: ',
            inputSelector: '#deep-dive-input',
            resultSelector: '#deep-dive-result'
        },
        'example': {
            endpoint: 'gemini-2.5-flash-preview-05-20',
            promptPrefix: 'أعطني أمثلة تطبيقية عملية وحالات استخدام للمفهوم التالي في الرؤية الحاسوبية: ',
            inputSelector: '#example-input',
            resultSelector: '#example-result'
        }
    };
    
    // Auth variables
    const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
    const firebaseConfig = JSON.parse(typeof __firebase_config !== 'undefined' ? __firebase_config : '{}');
    const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : undefined;

    const mainContentDisplay = document.getElementById('main-content-display');
    const chapterList = document.getElementById('chapter-list');
    const toolList = document.querySelector('.tool-list');
    const aiToolSections = document.querySelectorAll('.ai-tool-section');
    
    const db = {}; // Placeholder for Firestore
    const auth = {}; // Placeholder for Auth
    let userId = null;

    // Simulate Firebase setup
    // This part would be replaced with actual Firebase initialization and authentication logic
    async function setupFirebase() {
        console.log("Setting up Firebase...");
        console.log("App ID:", appId);
        console.log("Firebase Config:", firebaseConfig);
        console.log("Auth Token:", initialAuthToken);

        // Simulate a logged-in user
        userId = "guest_" + Math.random().toString(36).substring(2, 15);
        console.log("Simulated User ID:", userId);
        
        // You would normally initialize Firebase here
        // const app = initializeApp(firebaseConfig);
        // db = getFirestore(app);
        // auth = getAuth(app);
        // if (initialAuthToken) {
        //     signInWithCustomToken(auth, initialAuthToken);
        // } else {
        //     signInAnonymously(auth);
        // }
    }

    async function handleTool(toolType) {
        const toolInfo = toolsData[toolType];
        const inputElement = document.querySelector(toolInfo.inputSelector);
        const resultElement = document.querySelector(toolInfo.resultSelector);
        const prompt = inputElement.value.trim();

        if (!prompt) {
            resultElement.innerText = "الرجاء إدخال مفهوم للبحث عنه.";
            return;
        }

        resultElement.innerHTML = '... جاري البحث والتحليل ...';

        const fullPrompt = toolInfo.promptPrefix + prompt;

        try {
            const payload = {
                contents: [{ parts: [{ text: fullPrompt }] }],
                systemInstruction: {
                    parts: [{ text: "أنت خبير في الرؤية الحاسوبية. مهمتك هي تقديم شروح وأمثلة دقيقة ومبسطة للطلاب." }]
                }
            };

            const apiKey = "";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${toolInfo.endpoint}:generateContent?key=${apiKey}`;

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            const text = result?.candidates?.[0]?.content?.parts?.[0]?.text;
            
            if (text) {
                resultElement.innerText = text;
            } else {
                resultElement.innerText = "عذراً، لم أتمكن من الحصول على إجابة. يرجى المحاولة مرة أخرى.";
            }
        } catch (error) {
            console.error("API Error:", error);
            resultElement.innerText = "حدث خطأ أثناء الاتصال. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.";
        }
    }

    chapterList.addEventListener('click', (event) => {
        const targetLi = event.target.closest('li');
        if (targetLi) {
            const chapterKey = targetLi.dataset.chapter;
            const chapter = chapters[chapterKey];
            if (chapter) {
                mainContentDisplay.querySelector('h2').innerText = chapter.title;
                mainContentDisplay.querySelector('p').innerHTML = chapter.content;
            }
        }
    });

    toolList.addEventListener('click', (event) => {
        const targetLi = event.target.closest('li');
        if (targetLi) {
            const toolKey = targetLi.dataset.tool;
            aiToolSections.forEach(section => {
                section.style.display = 'none';
            });
            document.getElementById(`${toolKey}-tool`).style.display = 'flex';
        }
    });

    // Initial setup
    document.addEventListener('DOMContentLoaded', () => {
        setupFirebase();
    });

</script>

</body>
</html>
