<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرؤية الحاسوبية - المادة التعليمية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-100 text-gray-800">

    <!-- Module 1: Header and Navigation Placeholder -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-cyan-700">الرؤية الحاسوبية</h1>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="text-gray-600 hover:text-cyan-600 transition">الرئيسية</a>
                    <a href="#objectifs" class="text-gray-600 hover:text-cyan-600 transition">الأهداف</a>
                    <a href="#contenu" class="text-gray-600 hover:text-cyan-600 transition">المحتوى</a>
                    <a href="#evaluation" class="text-gray-600 hover:text-cyan-600 transition">التقييم</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-btn" class="text-gray-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                </div>
            </div>
            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-3">
                <a href="#home" class="block py-2 text-gray-600 hover:text-cyan-600 transition">الرئيسية</a>
                <a href="#objectifs" class="block py-2 text-gray-600 hover:text-cyan-600 transition">الأهداف</a>
                <a href="#contenu" class="block py-2 text-gray-600 hover:text-cyan-600 transition">المحتوى</a>
                <a href="#evaluation" class="block py-2 text-gray-600 hover:text-cyan-600 transition">التقييم</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12">

        <section id="home" class="text-center mb-20">
            <h2 class="text-4xl font-bold text-cyan-800 mb-4">ماستر ذكاء اصطناعي</h2>
            <p class="text-lg max-w-3xl mx-auto text-gray-700">
                تهدف هذه المادة إلى إعطاء نظرة شاملة عن مجال الرؤية الحاسوبية، بدءًا من تكوين الصور وهندستها، مرورًا بالقياس الضوئي والرقمنة، وانتهاءً بإسقاط المشاهد ثلاثية الأبعاد على متن الصورة. هذه المنصة التفاعلية مصممة لتكون دليلك لاستكشاف هذا المجال المثير.
            </p>
        </section>

        <section id="objectifs" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">أهداف المادة</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">تهدف المادة إلى تزويدك بفهم عميق للرؤية الاصطناعية، تغطي الجوانب النظرية والتطبيقية الأساسية لإتقان هذا المجال.</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-2">المفاهيم الأساسية</h3>
                    <p class="text-gray-600">تقديم المفاهيم الأساسية للإدراك البصري الاصطناعي.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🧠</div>
                    <h3 class="text-xl font-semibold mb-2">الأساليب الحديثة</h3>
                    <p class="text-gray-600">فهم الطرق الكلاسيكية والحديثة لمعالجة وتحليل الصور والفيديو.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-semibold mb-2">إتقان التعلم العميق</h3>
                    <p class="text-gray-600">إتقان تقنيات التعلم العميق المتقدمة مثل CNNs و Vision Transformers.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">💡</div>
                    <h3 class="text-xl font-semibold mb-2">الاتجاهات الجديدة</h3>
                    <p class="text-gray-600">اكتشاف الاتجاهات الجديدة مثل التعلم الذاتي والنماذج متعددة الوسائط.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🛠️</div>
                    <h3 class="text-xl font-semibold mb-2">المهارات العملية</h3>
                    <p class="text-gray-600">تطوير خبرة عملية مع أدوات مثل OpenCV, PyTorch, و TensorFlow.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold mb-2">المعرفة المسبقة</h3>
                    <p class="text-gray-600">يوصى بمعرفة مسبقة في الجبر الخطي، الاحتمالات، والبرمجة بلغة بايثون.</p>
                </div>
            </div>
        </section>

        <!-- Module 3, 4, 5: Course Content (Tabs) Placeholder -->
        <section id="contenu" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">محتوى المادة</h2>
                <!-- Tab buttons will be generated here by script.js -->
                <div id="tabs-container" class="flex flex-wrap justify-center gap-2 mb-8"></div>
                <!-- Tab content will be displayed here -->
                <div id="tab-content-container" class="bg-white p-8 rounded-lg shadow-lg min-h-[250px]"></div>
            </div>
        </section>

        <!-- Module 6: Evaluation Section Placeholder -->
        <section id="evaluation" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">التقييم والمراجع</h2>
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="section-card p-6">
                        <h3 class="text-2xl font-semibold mb-4 text-center">طريقة التقييم</h3>
                        <div class="max-w-[350px] mx-auto h-[350px]">
                            <canvas id="evaluationChart"></canvas>
                        </div>
                    </div>
                    <div class="section-card p-8">
                        <h3 class="text-2xl font-semibold mb-6">المراجع الببليوغرافية</h3>
                        <ul class="space-y-6 text-right">
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: Algorithms and Applications (2nd ed.)</p>
                                <p class="text-sm text-gray-500">Szeliski, R. (2022). Springer.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: A Modern Approach (3rd ed.)</p>
                                <p class="text-sm text-gray-500">Forsyth, D. A. (2023). Pearson.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Deep Learning (1st ed.)</p>
                                <p class="text-sm text-gray-500">Goodfellow, I., Bengio, Y., & Courville, A. (2016). MIT Press.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Module 1: Footer Placeholder -->
    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-6 text-center">
            <p>&copy; 2025 الرؤية الحاسوبية - ماستر ذكاء اصطناعي</p>
        </div>
    </footer>

    <script>
    // --------------------------------------------------------------------------
    // !! تحذير أمني: لا تضع مفتاح API الخاص بك هنا مباشرة في بيئة الإنتاج !!
    // !! هذا لأغراض العرض فقط. في تطبيق حقيقي، يجب حماية المفتاح.   !!
    // --------------------------------------------------------------------------
    const API_KEY = "AIzaSyBuOqfYsA8LBWapVVLUR-Z3pEVmOoy23yM"; // <-- ضع مفتاح API الجديد والآمن هنا

    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-preview-0514:generateContent?key=${API_KEY}`;

    /**
     * دالة عامة لاستدعاء Gemini API
     * @param {object} payload - البيانات التي سيتم إرسالها إلى API
     * @returns {Promise<object>} - الرد من API
     */
    async function callGeminiApi(payload) {
        const geminiContentArea = document.getElementById('gemini-content-area');
        geminiContentArea.innerHTML = `<div class="flex items-center justify-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>جاري التفكير...</p></div>`;

        try {
            const response = await fetch(API_URL, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`API call failed with status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error calling Gemini API:', error);
            geminiContentArea.innerHTML = `<p class="text-red-500">حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. تأكد من صحة مفتاح API الخاص بك.</p>`;
            throw error;
        }
    }

    /**
     * يعرض نصًا منسقًا في منطقة المحتوى
     * @param {string} title - عنوان المحتوى
     * @param {string} text - النص المراد عرضه
     */
    function displayTextResponse(title, text) {
        const geminiContentArea = document.getElementById('gemini-content-area');
        // استبدال علامات النجمة لإنشاء قوائم HTML
        const formattedText = text.replace(/\*\s(.*?)(?=\n\*|\n$)/g, '<li>$1</li>').replace(/\*\*/g, '<strong>').replace(/\* /g, '<li>');
        geminiContentArea.innerHTML = `<h4 class="font-bold text-lg mb-2">${title}</h4><ul class="list-disc list-inside space-y-2">${formattedText}</ul>`;
    }

    // --- دوال الأزرار التفاعلية ---

    async function generateSummary() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `لخص هذا المحتوى من مادة الرؤية الحاسوبية في 3 نقاط رئيسية باللغة العربية:\n\n"${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("ملخص الفصل:", text);
        }
    }

    async function extractKeyTerms() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `من هذا المحتوى، استخرج 3 مصطلحات أساسية في الرؤية الحاسوبية واشرح كل منها في جملة واحدة باللغة العربية.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            term: { type: "STRING" },
                            definition: { type: "STRING" }
                        }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const terms = JSON.parse(text);
            let html = '<h4 class="font-bold text-lg mb-2">مصطلحات أساسية:</h4><ul class="list-disc list-inside space-y-2">';
            terms.forEach(t => {
                html += `<li><strong>${t.term}:</strong> ${t.definition}</li>`;
            });
            html += '</ul>';
            document.getElementById('gemini-content-area').innerHTML = html;
        }
    }

    async function generateAnalogy() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `اشرح المفهوم الأساسي في هذا النص باستخدام مثال أو مقارنة بسيطة من الحياة اليومية لتبسيطه. النص: "${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("تبسيط بمثال:", text);
        }
    }

    async function generateQuiz() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `أنشئ سؤال اختيار من متعدد واحد فقط (سؤال واحد، 3 خيارات، وإجابة واحدة صحيحة) بناءً على هذا المحتوى.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "OBJECT",
                    properties: {
                        question: { type: "STRING" },
                        options: { type: "ARRAY", items: { type: "STRING" } },
                        answer: { type: "STRING" }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const quiz = JSON.parse(text);
            let html = `<h4 class="font-bold text-lg mb-2">اختبار سريع:</h4><p class="mb-2">${quiz.question}</p><div class="space-y-2">`;
            quiz.options.forEach(opt => {
                html += `<label class="block"><input type="radio" name="quiz-opt" value="${opt}" class="ml-2">${opt}</label>`;
            });
            html += `</div><button onclick="alert('الإجابة الصحيحة هي: ${quiz.answer}')" class="mt-4 bg-gray-200 text-gray-800 py-1 px-3 rounded-lg text-sm">إظهار الإجابة</button>`;
            document.getElementById('gemini-content-area').innerHTML = html;
        }
    }


    document.addEventListener('DOMContentLoaded', function () {
        console.log("Script loaded, DOM ready.");

        const courseContent = [
            {
                title: "الفصل 1: تمثيل الصور ومعالجتها",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تكوين الصور:</strong> الكاميرات، المستشعرات.</li><li><strong>الهندسة:</strong> الإحداثيات المتجانسة وتحويلاتها.</li><li><strong>القياس الضوئي (Photometry):</strong> إضاءة الشعاع والصورة.</li><li><strong>فضاءات الألوان:</strong> RGB, HSV, YCbCr.</li><li><strong>العمليات الأساسية:</strong> الفلترة، تحديد العتبة (Thresholding)، والتجزئة البسيطة.</li></ul>`
            },
            {
                title: "الفصل 2: كشف واستخلاص الخصائص",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النقاط المهمة (Points of Interest):</strong> Harris, SIFT, SURF, ORB.</li><li><strong>الواصفات (Descriptors):</strong> وصف الخصائص والمطابقة بينها.</li><li><strong>حالة عملية:</strong> تتبع الكائنات (Object Tracking).</li></ul>`
            },
            {
                title: "الفصل 3: التعرف على الكائنات والرؤية الهندسية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النماذج الهندسية:</strong> القطبية الثنائية (Epipolarity)، التحويل المتجانس (Homography).</li><li><strong>معايرة الكاميرا (Camera Calibration):</strong> وإعادة البناء ثلاثي الأبعاد.</li><li><strong>كشف وتتبع الكائنات:</strong> HOG, Viola-Jones.</li></ul>`
            },
            {
                title: "الفصل 4: CNNs للرؤية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>أساسيات الشبكات العصبونية الالتفافية.</strong></li><li><strong>البنى الكلاسيكية:</strong> LeNet, AlexNet, VGG.</li><li><strong>مشاكل شائعة:</strong> التخصيص المفرط (Overfitting)، التنظيم (Regularization)، نقل المعرفة (Transfer Learning).</li></ul>`
            },
            {
                title: "الفصل 5: CNNs متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>بنى حديثة:</strong> ResNet, DenseNet, EfficientNet.</li><li><strong>شبكات التجزئة (Segmentation):</strong> U-Net, Mask R-CNN.</li><li><strong>كشف الكائنات (Object Detection):</strong> YOLO, Faster R-CNN, DETR.</li></ul>`
            },
            {
                title: "الفصل 6: محولات الرؤية (ViT)",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>مبدأ عمل محولات الرؤية المطبقة على الصور.</strong></li><li><strong>مقارنة بين CNN و ViT.</strong></li><li><strong>تطبيقات حديثة:</strong> التصنيف، التجزئة، والكشف.</li></ul>`
            },
            {
                title: "الفصل 7: الرؤية ثلاثية الأبعاد والفيديو",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تقدير الحركة والتدفق البصري (Optical Flow).</strong></li><li><strong>الرؤية المجسمة (Stereo Vision).</strong></li><li><strong>تحليل الحركات في مقاطع الفيديو.</strong></li></ul>`
            },
            {
                title: "الفصل 8: الرؤية متعددة الوسائط والتعلم الذاتي",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>التعلم الذاتي المراقب (Self-supervised):</strong> SimCLR, MAE, MoCo.</li><li><strong>النماذج متعددة الوسائط (Multimodal):</strong> CLIP, BLIP-2.</li><li><strong>تطبيقات:</strong> البحث عن الصور بالنص، الإجابة على الأسئلة المرئية (VQA).</li></ul>`
            },
            {
                title: "الفصل 9: تطبيقات متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>الرؤية الطبية:</strong> تحليل الأشعة والصور الطبية الحيوية.</li><li><strong>القيادة الذاتية:</strong> كشف البيئة المحيطة.</li><li><strong>الواقع المعزز، القياسات الحيوية، والروبوتات.</strong></li></ul>`
            }
        ];

        const tabsContainer = document.getElementById('tabs-container');
        const contentContainer = document.getElementById('tab-content-container');

        if (!tabsContainer || !contentContainer) {
            console.error("Tab containers not found!");
            return;
        }

        function updateTabContent(item) {
            const buttonsHTML = `
                <div class="flex flex-wrap justify-center gap-3 mt-6 pt-4 border-t-2 border-gray-200">
                    <button onclick="generateSummary()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">✨ تلخيص</button>
                    <button onclick="generateQuiz()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🧠 إنشاء اختبار</button>
                    <button onclick="extractKeyTerms()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🔑 مصطلحات أساسية</button>
                    <button onclick="generateAnalogy()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">💡 تبسيط بمثال</button>
                </div>
            `;

            contentContainer.innerHTML = `
                <h3 class='text-2xl font-bold text-cyan-800 mb-4 text-right'>${item.title}</h3>
                <div class='text-gray-700 leading-relaxed' id='chapter-text'>${item.content}</div>
                ${buttonsHTML}
                <div id="gemini-content-area" class="mt-4 p-4 bg-gray-50 rounded-lg min-h-[50px] border border-gray-200"></div>
            `;
        }

        courseContent.forEach((item, index) => {
            const tabButton = document.createElement('button');
            tabButton.textContent = item.title;
            tabButton.className = 'tab-btn px-4 py-2 text-sm font-semibold border-2 border-gray-300 rounded-full transition hover:bg-cyan-500 hover:text-white hover:border-cyan-500 focus:outline-none';
            
            if (index === 0) {
                tabButton.classList.add('active');
                updateTabContent(item);
            }

            tabButton.addEventListener('click', () => {
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                tabButton.classList.add('active');
                updateTabContent(item);
            });

            tabsContainer.appendChild(tabButton);
        });

        const evaluationChartElement = document.getElementById('evaluationChart');
        if (evaluationChartElement) {
            const ctx = evaluationChartElement.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['امتحان فصلي (60%)', 'تقييم مستمر (40%)'],
                    datasets: [{
                        data: [60, 40],
                        backgroundColor: ['#0891b2', '#f97316'],
                        borderColor: '#f3f4f6',
                        borderWidth: 4,
                        hoverOffset: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: { 
                        legend: { position: 'bottom', labels: { padding: 20, font: { family: "'Cairo', sans-serif", size: 14 } } },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

        const menuBtn = document.getElementById('menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        if(menuBtn && mobileMenu) {
            menuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        }
    });
    </script>
</body>
</html>
