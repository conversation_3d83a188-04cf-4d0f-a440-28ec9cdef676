<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرؤية الحاسوبية - المادة التعليمية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- Additional libraries for interactive tools -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
</head>
<body class="bg-gray-100 text-gray-800">

    <!-- Module 1: Header and Navigation Placeholder -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-cyan-700">الرؤية الحاسوبية</h1>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="text-gray-600 hover:text-cyan-600 transition">الرئيسية</a>
                    <a href="#objectifs" class="text-gray-600 hover:text-cyan-600 transition">الأهداف</a>
                    <a href="#contenu" class="text-gray-600 hover:text-cyan-600 transition">المحتوى</a>
                    <a href="#tools" class="text-gray-600 hover:text-cyan-600 transition">الأدوات التفاعلية</a>
                    <a href="#evaluation" class="text-gray-600 hover:text-cyan-600 transition">التقييم</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-btn" class="text-gray-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                </div>
            </div>
            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-3">
                <a href="#home" class="block py-2 text-gray-600 hover:text-cyan-600 transition">الرئيسية</a>
                <a href="#objectifs" class="block py-2 text-gray-600 hover:text-cyan-600 transition">الأهداف</a>
                <a href="#contenu" class="block py-2 text-gray-600 hover:text-cyan-600 transition">المحتوى</a>
                <a href="#tools" class="block py-2 text-gray-600 hover:text-cyan-600 transition">الأدوات التفاعلية</a>
                <a href="#evaluation" class="block py-2 text-gray-600 hover:text-cyan-600 transition">التقييم</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12">

        <section id="home" class="text-center mb-20">
            <h2 class="text-4xl font-bold text-cyan-800 mb-4">ماستر ذكاء اصطناعي</h2>
            <p class="text-lg max-w-3xl mx-auto text-gray-700">
                تهدف هذه المادة إلى إعطاء نظرة شاملة عن مجال الرؤية الحاسوبية، بدءًا من تكوين الصور وهندستها، مرورًا بالقياس الضوئي والرقمنة، وانتهاءً بإسقاط المشاهد ثلاثية الأبعاد على متن الصورة. هذه المنصة التفاعلية مصممة لتكون دليلك لاستكشاف هذا المجال المثير.
            </p>
        </section>

        <section id="objectifs" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">أهداف المادة</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">تهدف المادة إلى تزويدك بفهم عميق للرؤية الاصطناعية، تغطي الجوانب النظرية والتطبيقية الأساسية لإتقان هذا المجال.</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-2">المفاهيم الأساسية</h3>
                    <p class="text-gray-600">تقديم المفاهيم الأساسية للإدراك البصري الاصطناعي.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🧠</div>
                    <h3 class="text-xl font-semibold mb-2">الأساليب الحديثة</h3>
                    <p class="text-gray-600">فهم الطرق الكلاسيكية والحديثة لمعالجة وتحليل الصور والفيديو.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-semibold mb-2">إتقان التعلم العميق</h3>
                    <p class="text-gray-600">إتقان تقنيات التعلم العميق المتقدمة مثل CNNs و Vision Transformers.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">💡</div>
                    <h3 class="text-xl font-semibold mb-2">الاتجاهات الجديدة</h3>
                    <p class="text-gray-600">اكتشاف الاتجاهات الجديدة مثل التعلم الذاتي والنماذج متعددة الوسائط.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🛠️</div>
                    <h3 class="text-xl font-semibold mb-2">المهارات العملية</h3>
                    <p class="text-gray-600">تطوير خبرة عملية مع أدوات مثل OpenCV, PyTorch, و TensorFlow.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold mb-2">المعرفة المسبقة</h3>
                    <p class="text-gray-600">يوصى بمعرفة مسبقة في الجبر الخطي، الاحتمالات، والبرمجة بلغة بايثون.</p>
                </div>
            </div>
        </section>

        <!-- Module 3, 4, 5: Course Content (Tabs) Placeholder -->
        <section id="contenu" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">محتوى المادة</h2>
                <!-- Tab buttons will be generated here by script.js -->
                <div id="tabs-container" class="flex flex-wrap justify-center gap-2 mb-8"></div>
                <!-- Tab content will be displayed here -->
                <div id="tab-content-container" class="bg-white p-8 rounded-lg shadow-lg min-h-[250px]"></div>
            </div>
        </section>

        <!-- Interactive Educational Tools Section -->
        <section id="tools" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">الأدوات التعليمية التفاعلية</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">مجموعة من الأدوات التفاعلية المتقدمة لتعزيز تجربة التعلم وفهم مفاهيم الرؤية الحاسوبية بشكل عملي وتفاعلي.</p>
            </div>

            <!-- Tools Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Image Processing Simulator -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🖼️</div>
                    <h3 class="text-xl font-semibold mb-2">محاكي معالجة الصور</h3>
                    <p class="text-gray-600 mb-4">جرب تطبيق المرشحات والتحويلات على الصور مباشرة</p>
                    <button onclick="openImageProcessor()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>

                <!-- 3D Visualization -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-2">التصور ثلاثي الأبعاد</h3>
                    <p class="text-gray-600 mb-4">استكشف المفاهيم الهندسية والتحويلات ثلاثية الأبعاد</p>
                    <button onclick="open3DVisualization()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>

                <!-- Algorithm Comparison -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">⚖️</div>
                    <h3 class="text-xl font-semibold mb-2">مقارنة الخوارزميات</h3>
                    <p class="text-gray-600 mb-4">قارن بين أداء الخوارزميات المختلفة بصريًا</p>
                    <button onclick="openAlgorithmComparison()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>

                <!-- Progress Tracker -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">📊</div>
                    <h3 class="text-xl font-semibold mb-2">متتبع التقدم</h3>
                    <p class="text-gray-600 mb-4">تتبع تقدمك التعليمي وإنجازاتك في المادة</p>
                    <button onclick="openProgressTracker()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>

                <!-- Code Editor -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">💻</div>
                    <h3 class="text-xl font-semibold mb-2">محرر الكود التفاعلي</h3>
                    <p class="text-gray-600 mb-4">اكتب وجرب كود Python للرؤية الحاسوبية مباشرة</p>
                    <button onclick="openCodeEditor()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>

                <!-- Concept Map Generator -->
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🗺️</div>
                    <h3 class="text-xl font-semibold mb-2">مولد الخرائط المفاهيمية</h3>
                    <p class="text-gray-600 mb-4">أنشئ خرائط مفاهيمية تفاعلية للمواضيع</p>
                    <button onclick="openConceptMap()" class="bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition">تجربة الأداة</button>
                </div>
            </div>

            <!-- Tool Modal Container -->
            <div id="tool-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                    <div class="flex justify-between items-center p-6 border-b">
                        <h3 id="modal-title" class="text-2xl font-bold text-cyan-800"></h3>
                        <button onclick="closeToolModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                    </div>
                    <div id="modal-content" class="p-6">
                        <!-- Tool content will be loaded here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Module 6: Evaluation Section Placeholder -->
        <section id="evaluation" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">التقييم والمراجع</h2>
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="section-card p-6">
                        <h3 class="text-2xl font-semibold mb-4 text-center">طريقة التقييم</h3>
                        <div class="max-w-[350px] mx-auto h-[350px]">
                            <canvas id="evaluationChart"></canvas>
                        </div>
                    </div>
                    <div class="section-card p-8">
                        <h3 class="text-2xl font-semibold mb-6">المراجع الببليوغرافية</h3>
                        <ul class="space-y-6 text-right">
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: Algorithms and Applications (2nd ed.)</p>
                                <p class="text-sm text-gray-500">Szeliski, R. (2022). Springer.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: A Modern Approach (3rd ed.)</p>
                                <p class="text-sm text-gray-500">Forsyth, D. A. (2023). Pearson.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Deep Learning (1st ed.)</p>
                                <p class="text-sm text-gray-500">Goodfellow, I., Bengio, Y., & Courville, A. (2016). MIT Press.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Module 1: Footer Placeholder -->
    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-6 text-center">
            <p>&copy; 2025 الرؤية الحاسوبية - ماستر ذكاء اصطناعي</p>
        </div>
    </footer>

    <script>
    // --------------------------------------------------------------------------
    // !! تحذير أمني: لا تضع مفتاح API الخاص بك هنا مباشرة في بيئة الإنتاج !!
    // !! هذا لأغراض العرض فقط. في تطبيق حقيقي، يجب حماية المفتاح.   !!
    // --------------------------------------------------------------------------
    const API_KEY = "AIzaSyBF9TW-VQhxMXRbEIt4pLX8LaWHRp2aKhk"; // <-- ضع مفتاح API الجديد والآمن هنا

    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-preview-0514:generateContent?key=${API_KEY}`;

    // Global variables for interactive tools
    let currentTool = null;
    let progressData = JSON.parse(localStorage.getItem('cvProgress') || '{}');

    /**
     * دالة عامة لاستدعاء Gemini API
     * @param {object} payload - البيانات التي سيتم إرسالها إلى API
     * @returns {Promise<object>} - الرد من API
     */
    async function callGeminiApi(payload) {
        const geminiContentArea = document.getElementById('gemini-content-area');
        if (geminiContentArea) {
            geminiContentArea.innerHTML = `<div class="flex items-center justify-center space-x-2"><svg class="animate-spin h-5 w-5 text-cyan-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><p>جاري التفكير...</p></div>`;
        }

        try {
            const response = await fetch(API_URL, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`API call failed with status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error calling Gemini API:', error);
            if (geminiContentArea) {
                geminiContentArea.innerHTML = `<p class="text-red-500">حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. تأكد من صحة مفتاح API الخاص بك.</p>`;
            }
            throw error;
        }
    }

    // === Interactive Tools Functions ===

    function openToolModal(title, content) {
        document.getElementById('modal-title').textContent = title;
        document.getElementById('modal-content').innerHTML = content;
        document.getElementById('tool-modal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closeToolModal() {
        document.getElementById('tool-modal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        if (currentTool && currentTool.cleanup) {
            currentTool.cleanup();
        }
        currentTool = null;
    }

    function openImageProcessor() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">تحميل الصورة</h4>
                    <input type="file" id="imageInput" accept="image/*" class="mb-4 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-cyan-50 file:text-cyan-700 hover:file:bg-cyan-100">
                    <canvas id="originalCanvas" class="border border-gray-300 max-w-full"></canvas>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">المرشحات والتأثيرات</h4>
                    <div class="space-y-2 mb-4">
                        <button onclick="applyFilter('grayscale')" class="block w-full bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">تحويل إلى رمادي</button>
                        <button onclick="applyFilter('blur')" class="block w-full bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">تشويش</button>
                        <button onclick="applyFilter('edge')" class="block w-full bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">كشف الحواف</button>
                        <button onclick="applyFilter('brightness')" class="block w-full bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">زيادة السطوع</button>
                        <button onclick="resetImage()" class="block w-full bg-red-200 hover:bg-red-300 px-4 py-2 rounded">إعادة تعيين</button>
                    </div>
                    <canvas id="processedCanvas" class="border border-gray-300 max-w-full"></canvas>
                </div>
            </div>
        `;
        openToolModal('محاكي معالجة الصور', content);
        initImageProcessor();
    }

    function open3DVisualization() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">التحكم في المشهد ثلاثي الأبعاد</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران X:</label>
                            <input type="range" id="rotationX" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران Y:</label>
                            <input type="range" id="rotationY" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران Z:</label>
                            <input type="range" id="rotationZ" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">التكبير:</label>
                            <input type="range" id="zoom" min="1" max="10" value="5" step="0.1" class="w-full" onchange="update3DScene()">
                        </div>
                        <button onclick="reset3DScene()" class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded w-full">إعادة تعيين</button>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">المشهد ثلاثي الأبعاد</h4>
                    <div id="threejs-container" class="border border-gray-300 bg-gray-100" style="width: 100%; height: 400px;"></div>
                </div>
            </div>
        `;
        openToolModal('التصور ثلاثي الأبعاد', content);
        init3DVisualization();
    }

    function openAlgorithmComparison() {
        const content = `
            <div class="space-y-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold mb-4">اختر الخوارزميات للمقارنة</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" value="cnn" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>الشبكات العصبونية الالتفافية (CNN)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="vit" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>محولات الرؤية (Vision Transformers)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="svm" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>آلة الدعم الشعاعي (SVM)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="knn" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>أقرب الجيران (K-NN)</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">معايير المقارنة</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" value="accuracy" class="ml-2" checked onchange="updateAlgorithmComparison()">
                                <span>الدقة</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="speed" class="ml-2" checked onchange="updateAlgorithmComparison()">
                                <span>السرعة</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="memory" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>استهلاك الذاكرة</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div id="comparison-chart-container">
                    <canvas id="comparisonChart" width="400" height="200"></canvas>
                </div>
            </div>
        `;
        openToolModal('مقارنة الخوارزميات', content);
        initAlgorithmComparison();
    }

    /**
     * يعرض نصًا منسقًا في منطقة المحتوى
     * @param {string} title - عنوان المحتوى
     * @param {string} text - النص المراد عرضه
     */
    function displayTextResponse(title, text) {
        const geminiContentArea = document.getElementById('gemini-content-area');
        if (geminiContentArea) {
            // استبدال علامات النجمة لإنشاء قوائم HTML
            const formattedText = text.replace(/\*\s(.*?)(?=\n\*|\n$)/g, '<li>$1</li>').replace(/\*\*/g, '<strong>').replace(/\* /g, '<li>');
            geminiContentArea.innerHTML = `<h4 class="font-bold text-lg mb-2">${title}</h4><ul class="list-disc list-inside space-y-2">${formattedText}</ul>`;
        }
    }

    function openProgressTracker() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">إحصائيات التقدم</h4>
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span>الفصول المكتملة</span>
                                <span id="completed-chapters">0/9</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="progress-bar" class="bg-cyan-600 h-2 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span>الاختبارات المكتملة</span>
                                <span id="completed-quizzes">0</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span>الأدوات المستخدمة</span>
                                <span id="tools-used">0/6</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الإنجازات</h4>
                    <div id="achievements-list" class="space-y-2">
                        <!-- Achievements will be populated here -->
                    </div>
                    <button onclick="resetProgress()" class="mt-4 bg-red-200 hover:bg-red-300 px-4 py-2 rounded w-full">إعادة تعيين التقدم</button>
                </div>
            </div>
        `;
        openToolModal('متتبع التقدم', content);
        initProgressTracker();
    }

    function openCodeEditor() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">محرر الكود</h4>
                    <div class="mb-4">
                        <select id="code-template" onchange="loadCodeTemplate()" class="w-full p-2 border border-gray-300 rounded">
                            <option value="">اختر قالب كود...</option>
                            <option value="image-loading">تحميل وعرض صورة</option>
                            <option value="edge-detection">كشف الحواف</option>
                            <option value="color-conversion">تحويل الألوان</option>
                            <option value="histogram">رسم الهيستوجرام</option>
                        </select>
                    </div>
                    <textarea id="code-editor" class="w-full h-64 p-4 border border-gray-300 rounded font-mono text-sm" placeholder="اكتب كود Python هنا..."></textarea>
                    <button onclick="runCode()" class="mt-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">تشغيل الكود</button>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">النتيجة</h4>
                    <div id="code-output" class="w-full h-80 p-4 border border-gray-300 rounded bg-gray-50 font-mono text-sm overflow-y-auto">
                        النتيجة ستظهر هنا...
                    </div>
                </div>
            </div>
        `;
        openToolModal('محرر الكود التفاعلي', content);
        initCodeEditor();
    }

    function openConceptMap() {
        const content = `
            <div class="space-y-6">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold">خريطة المفاهيم التفاعلية</h4>
                    <div class="space-x-2">
                        <button onclick="generateConceptMap()" class="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">إنشاء خريطة جديدة</button>
                        <button onclick="resetConceptMap()" class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">إعادة تعيين</button>
                    </div>
                </div>
                <div id="concept-map-container" class="border border-gray-300 bg-white" style="width: 100%; height: 500px; position: relative;">
                    <div class="flex items-center justify-center h-full text-gray-500">
                        انقر على "إنشاء خريطة جديدة" لبدء إنشاء خريطة المفاهيم
                    </div>
                </div>
            </div>
        `;
        openToolModal('مولد الخرائط المفاهيمية', content);
        initConceptMap();
    }

    // === Implementation Functions ===

    function initImageProcessor() {
        const imageInput = document.getElementById('imageInput');
        if (imageInput) {
            imageInput.addEventListener('change', handleImageUpload);
        }
    }

    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    const originalCanvas = document.getElementById('originalCanvas');
                    const processedCanvas = document.getElementById('processedCanvas');

                    if (originalCanvas && processedCanvas) {
                        const ctx1 = originalCanvas.getContext('2d');
                        const ctx2 = processedCanvas.getContext('2d');

                        originalCanvas.width = processedCanvas.width = Math.min(img.width, 300);
                        originalCanvas.height = processedCanvas.height = Math.min(img.height, 300);

                        ctx1.drawImage(img, 0, 0, originalCanvas.width, originalCanvas.height);
                        ctx2.drawImage(img, 0, 0, processedCanvas.width, processedCanvas.height);

                        currentTool = { originalImageData: ctx1.getImageData(0, 0, originalCanvas.width, originalCanvas.height) };
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    // --- دوال الأزرار التفاعلية ---

    async function generateSummary() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `لخص هذا المحتوى من مادة الرؤية الحاسوبية في 3 نقاط رئيسية باللغة العربية:\n\n"${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("ملخص الفصل:", text);
        }
    }

    async function extractKeyTerms() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `من هذا المحتوى، استخرج 3 مصطلحات أساسية في الرؤية الحاسوبية واشرح كل منها في جملة واحدة باللغة العربية.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            term: { type: "STRING" },
                            definition: { type: "STRING" }
                        }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const terms = JSON.parse(text);
            let html = '<h4 class="font-bold text-lg mb-2">مصطلحات أساسية:</h4><ul class="list-disc list-inside space-y-2">';
            terms.forEach(t => {
                html += `<li><strong>${t.term}:</strong> ${t.definition}</li>`;
            });
            html += '</ul>';
            document.getElementById('gemini-content-area').innerHTML = html;
        }
    }

    async function generateAnalogy() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `اشرح المفهوم الأساسي في هذا النص باستخدام مثال أو مقارنة بسيطة من الحياة اليومية لتبسيطه. النص: "${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("تبسيط بمثال:", text);
        }
    }

    async function generateQuiz() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `أنشئ سؤال اختيار من متعدد واحد فقط (سؤال واحد، 3 خيارات، وإجابة واحدة صحيحة) بناءً على هذا المحتوى.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "OBJECT",
                    properties: {
                        question: { type: "STRING" },
                        options: { type: "ARRAY", items: { type: "STRING" } },
                        answer: { type: "STRING" }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const quiz = JSON.parse(text);
            let html = `<h4 class="font-bold text-lg mb-2">اختبار سريع:</h4><p class="mb-2">${quiz.question}</p><div class="space-y-2">`;
            quiz.options.forEach(opt => {
                html += `<label class="block"><input type="radio" name="quiz-opt" value="${opt}" class="ml-2">${opt}</label>`;
            });
            html += `</div><button onclick="alert('الإجابة الصحيحة هي: ${quiz.answer}')" class="mt-4 bg-gray-200 text-gray-800 py-1 px-3 rounded-lg text-sm">إظهار الإجابة</button>`;
            document.getElementById('gemini-content-area').innerHTML = html;

            // Update progress
            updateProgress('quizzes', 1);
        }
    }

    // === Image Processing Functions ===

    function applyFilter(filterType) {
        const processedCanvas = document.getElementById('processedCanvas');
        if (!processedCanvas || !currentTool || !currentTool.originalImageData) return;

        const ctx = processedCanvas.getContext('2d');
        const imageData = new ImageData(
            new Uint8ClampedArray(currentTool.originalImageData.data),
            currentTool.originalImageData.width,
            currentTool.originalImageData.height
        );

        const data = imageData.data;

        switch(filterType) {
            case 'grayscale':
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                    data[i] = data[i + 1] = data[i + 2] = gray;
                }
                break;
            case 'blur':
                // Simple blur effect
                for (let i = 0; i < data.length; i += 4) {
                    if (i > 4 && i < data.length - 4) {
                        data[i] = (data[i-4] + data[i] + data[i+4]) / 3;
                        data[i+1] = (data[i-3] + data[i+1] + data[i+5]) / 3;
                        data[i+2] = (data[i-2] + data[i+2] + data[i+6]) / 3;
                    }
                }
                break;
            case 'edge':
                // Simple edge detection
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                    const edge = gray > 128 ? 255 : 0;
                    data[i] = data[i + 1] = data[i + 2] = edge;
                }
                break;
            case 'brightness':
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.min(255, data[i] + 50);
                    data[i + 1] = Math.min(255, data[i + 1] + 50);
                    data[i + 2] = Math.min(255, data[i + 2] + 50);
                }
                break;
        }

        ctx.putImageData(imageData, 0, 0);
        updateProgress('tools', 'image-processor');
    }

    function resetImage() {
        const processedCanvas = document.getElementById('processedCanvas');
        if (!processedCanvas || !currentTool || !currentTool.originalImageData) return;

        const ctx = processedCanvas.getContext('2d');
        ctx.putImageData(currentTool.originalImageData, 0, 0);
    }


    // === 3D Visualization Functions ===

    function init3DVisualization() {
        if (typeof THREE === 'undefined') {
            document.getElementById('threejs-container').innerHTML = '<p class="text-red-500">مكتبة Three.js غير متوفرة</p>';
            return;
        }

        const container = document.getElementById('threejs-container');
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();

        renderer.setSize(container.offsetWidth, container.offsetHeight);
        container.appendChild(renderer.domElement);

        // Create a cube
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ color: 0x0891b2, wireframe: true });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);

        camera.position.z = 5;

        currentTool = { scene, camera, renderer, cube, animate: () => {
            requestAnimationFrame(currentTool.animate);
            renderer.render(scene, camera);
        }};

        currentTool.animate();
        updateProgress('tools', '3d-visualization');
    }

    function update3DScene() {
        if (!currentTool || !currentTool.cube) return;

        const rotX = document.getElementById('rotationX').value * Math.PI / 180;
        const rotY = document.getElementById('rotationY').value * Math.PI / 180;
        const rotZ = document.getElementById('rotationZ').value * Math.PI / 180;
        const zoom = document.getElementById('zoom').value;

        currentTool.cube.rotation.x = rotX;
        currentTool.cube.rotation.y = rotY;
        currentTool.cube.rotation.z = rotZ;
        currentTool.camera.position.z = zoom;
    }

    function reset3DScene() {
        document.getElementById('rotationX').value = 0;
        document.getElementById('rotationY').value = 0;
        document.getElementById('rotationZ').value = 0;
        document.getElementById('zoom').value = 5;
        update3DScene();
    }

    // === Algorithm Comparison Functions ===

    function initAlgorithmComparison() {
        updateAlgorithmComparison();
        updateProgress('tools', 'algorithm-comparison');
    }

    function updateAlgorithmComparison() {
        const algorithms = Array.from(document.querySelectorAll('input[type="checkbox"][value]'))
            .filter(cb => cb.checked && ['cnn', 'vit', 'svm', 'knn'].includes(cb.value))
            .map(cb => cb.value);

        const metrics = Array.from(document.querySelectorAll('input[type="checkbox"][value]'))
            .filter(cb => cb.checked && ['accuracy', 'speed', 'memory'].includes(cb.value))
            .map(cb => cb.value);

        if (algorithms.length === 0 || metrics.length === 0) return;

        // Sample data for demonstration
        const data = {
            cnn: { accuracy: 85, speed: 70, memory: 60 },
            vit: { accuracy: 90, speed: 50, memory: 40 },
            svm: { accuracy: 75, speed: 90, memory: 95 },
            knn: { accuracy: 70, speed: 95, memory: 98 }
        };

        const canvas = document.getElementById('comparisonChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Simple bar chart
        const barWidth = canvas.width / (algorithms.length * metrics.length + algorithms.length);
        let x = 20;

        algorithms.forEach((alg, algIndex) => {
            metrics.forEach((metric, metricIndex) => {
                const value = data[alg][metric];
                const barHeight = (value / 100) * (canvas.height - 40);

                ctx.fillStyle = `hsl(${algIndex * 60}, 70%, 50%)`;
                ctx.fillRect(x, canvas.height - barHeight - 20, barWidth - 5, barHeight);

                // Label
                ctx.fillStyle = 'black';
                ctx.font = '10px Arial';
                ctx.fillText(`${alg}-${metric}`, x, canvas.height - 5);

                x += barWidth;
            });
            x += 10;
        });
    }

    // === Progress Tracking Functions ===

    function initProgressTracker() {
        updateProgressDisplay();
        updateProgress('tools', 'progress-tracker');
    }

    function updateProgress(type, value) {
        if (!progressData[type]) progressData[type] = new Set();

        if (type === 'quizzes') {
            progressData[type] = (progressData[type] || 0) + value;
        } else if (type === 'tools') {
            progressData[type].add(value);
        } else if (type === 'chapters') {
            progressData[type].add(value);
        }

        localStorage.setItem('cvProgress', JSON.stringify(progressData, (key, value) => {
            if (value instanceof Set) return Array.from(value);
            return value;
        }));

        updateProgressDisplay();
    }

    function updateProgressDisplay() {
        const completedChapters = document.getElementById('completed-chapters');
        const progressBar = document.getElementById('progress-bar');
        const completedQuizzes = document.getElementById('completed-quizzes');
        const toolsUsed = document.getElementById('tools-used');

        if (completedChapters) {
            const chapters = progressData.chapters ? progressData.chapters.length || progressData.chapters.size : 0;
            completedChapters.textContent = `${chapters}/9`;
            if (progressBar) {
                progressBar.style.width = `${(chapters / 9) * 100}%`;
            }
        }

        if (completedQuizzes) {
            completedQuizzes.textContent = progressData.quizzes || 0;
        }

        if (toolsUsed) {
            const tools = progressData.tools ? progressData.tools.length || progressData.tools.size : 0;
            toolsUsed.textContent = `${tools}/6`;
        }

        updateAchievements();
    }

    function updateAchievements() {
        const achievementsList = document.getElementById('achievements-list');
        if (!achievementsList) return;

        const achievements = [];
        const chapters = progressData.chapters ? progressData.chapters.length || progressData.chapters.size : 0;
        const tools = progressData.tools ? progressData.tools.length || progressData.tools.size : 0;
        const quizzes = progressData.quizzes || 0;

        if (chapters >= 3) achievements.push('🎓 مستكشف المعرفة - أكمل 3 فصول');
        if (tools >= 3) achievements.push('🛠️ خبير الأدوات - استخدم 3 أدوات مختلفة');
        if (quizzes >= 5) achievements.push('🧠 عبقري الاختبارات - أكمل 5 اختبارات');
        if (chapters === 9) achievements.push('🏆 سيد الرؤية الحاسوبية - أكمل جميع الفصول');

        achievementsList.innerHTML = achievements.length > 0
            ? achievements.map(a => `<div class="bg-yellow-100 p-2 rounded">${a}</div>`).join('')
            : '<div class="text-gray-500">لا توجد إنجازات بعد</div>';
    }

    function resetProgress() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            progressData = {};
            localStorage.removeItem('cvProgress');
            updateProgressDisplay();
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        console.log("Script loaded, DOM ready.");

        // Initialize progress data from localStorage
        const stored = localStorage.getItem('cvProgress');
        if (stored) {
            const parsed = JSON.parse(stored);
            progressData = {};
            Object.keys(parsed).forEach(key => {
                if (Array.isArray(parsed[key])) {
                    progressData[key] = new Set(parsed[key]);
                } else {
                    progressData[key] = parsed[key];
                }
            });
        }

        const courseContent = [
            {
                title: "الفصل 1: تمثيل الصور ومعالجتها",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تكوين الصور:</strong> الكاميرات، المستشعرات.</li><li><strong>الهندسة:</strong> الإحداثيات المتجانسة وتحويلاتها.</li><li><strong>القياس الضوئي (Photometry):</strong> إضاءة الشعاع والصورة.</li><li><strong>فضاءات الألوان:</strong> RGB, HSV, YCbCr.</li><li><strong>العمليات الأساسية:</strong> الفلترة، تحديد العتبة (Thresholding)، والتجزئة البسيطة.</li></ul>`
            },
            {
                title: "الفصل 2: كشف واستخلاص الخصائص",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النقاط المهمة (Points of Interest):</strong> Harris, SIFT, SURF, ORB.</li><li><strong>الواصفات (Descriptors):</strong> وصف الخصائص والمطابقة بينها.</li><li><strong>حالة عملية:</strong> تتبع الكائنات (Object Tracking).</li></ul>`
            },
            {
                title: "الفصل 3: التعرف على الكائنات والرؤية الهندسية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النماذج الهندسية:</strong> القطبية الثنائية (Epipolarity)، التحويل المتجانس (Homography).</li><li><strong>معايرة الكاميرا (Camera Calibration):</strong> وإعادة البناء ثلاثي الأبعاد.</li><li><strong>كشف وتتبع الكائنات:</strong> HOG, Viola-Jones.</li></ul>`
            },
            {
                title: "الفصل 4: CNNs للرؤية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>أساسيات الشبكات العصبونية الالتفافية.</strong></li><li><strong>البنى الكلاسيكية:</strong> LeNet, AlexNet, VGG.</li><li><strong>مشاكل شائعة:</strong> التخصيص المفرط (Overfitting)، التنظيم (Regularization)، نقل المعرفة (Transfer Learning).</li></ul>`
            },
            {
                title: "الفصل 5: CNNs متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>بنى حديثة:</strong> ResNet, DenseNet, EfficientNet.</li><li><strong>شبكات التجزئة (Segmentation):</strong> U-Net, Mask R-CNN.</li><li><strong>كشف الكائنات (Object Detection):</strong> YOLO, Faster R-CNN, DETR.</li></ul>`
            },
            {
                title: "الفصل 6: محولات الرؤية (ViT)",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>مبدأ عمل محولات الرؤية المطبقة على الصور.</strong></li><li><strong>مقارنة بين CNN و ViT.</strong></li><li><strong>تطبيقات حديثة:</strong> التصنيف، التجزئة، والكشف.</li></ul>`
            },
            {
                title: "الفصل 7: الرؤية ثلاثية الأبعاد والفيديو",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تقدير الحركة والتدفق البصري (Optical Flow).</strong></li><li><strong>الرؤية المجسمة (Stereo Vision).</strong></li><li><strong>تحليل الحركات في مقاطع الفيديو.</strong></li></ul>`
            },
            {
                title: "الفصل 8: الرؤية متعددة الوسائط والتعلم الذاتي",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>التعلم الذاتي المراقب (Self-supervised):</strong> SimCLR, MAE, MoCo.</li><li><strong>النماذج متعددة الوسائط (Multimodal):</strong> CLIP, BLIP-2.</li><li><strong>تطبيقات:</strong> البحث عن الصور بالنص، الإجابة على الأسئلة المرئية (VQA).</li></ul>`
            },
            {
                title: "الفصل 9: تطبيقات متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>الرؤية الطبية:</strong> تحليل الأشعة والصور الطبية الحيوية.</li><li><strong>القيادة الذاتية:</strong> كشف البيئة المحيطة.</li><li><strong>الواقع المعزز، القياسات الحيوية، والروبوتات.</strong></li></ul>`
            }
        ];

        const tabsContainer = document.getElementById('tabs-container');
        const contentContainer = document.getElementById('tab-content-container');

        if (!tabsContainer || !contentContainer) {
            console.error("Tab containers not found!");
            return;
        }

        function updateTabContent(item) {
            const buttonsHTML = `
                <div class="flex flex-wrap justify-center gap-3 mt-6 pt-4 border-t-2 border-gray-200">
                    <button onclick="generateSummary()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">✨ تلخيص</button>
                    <button onclick="generateQuiz()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🧠 إنشاء اختبار</button>
                    <button onclick="extractKeyTerms()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🔑 مصطلحات أساسية</button>
                    <button onclick="generateAnalogy()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">💡 تبسيط بمثال</button>
                </div>
            `;

            contentContainer.innerHTML = `
                <h3 class='text-2xl font-bold text-cyan-800 mb-4 text-right'>${item.title}</h3>
                <div class='text-gray-700 leading-relaxed' id='chapter-text'>${item.content}</div>
                ${buttonsHTML}
                <div id="gemini-content-area" class="mt-4 p-4 bg-gray-50 rounded-lg min-h-[50px] border border-gray-200"></div>
            `;
        }

        courseContent.forEach((item, index) => {
            const tabButton = document.createElement('button');
            tabButton.textContent = item.title;
            tabButton.className = 'tab-btn px-4 py-2 text-sm font-semibold border-2 border-gray-300 rounded-full transition hover:bg-cyan-500 hover:text-white hover:border-cyan-500 focus:outline-none';
            
            if (index === 0) {
                tabButton.classList.add('active');
                updateTabContent(item);
            }

            tabButton.addEventListener('click', () => {
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                tabButton.classList.add('active');
                updateTabContent(item);
            });

            tabsContainer.appendChild(tabButton);
        });

        // === Code Editor Functions ===

        function initCodeEditor() {
            updateProgress('tools', 'code-editor');
        }

        function loadCodeTemplate() {
            const template = document.getElementById('code-template').value;
            const editor = document.getElementById('code-editor');

            const templates = {
                'image-loading': `import cv2
import matplotlib.pyplot as plt

# تحميل الصورة
image = cv2.imread('image.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

# عرض الصورة
plt.imshow(image_rgb)
plt.title('الصورة الأصلية')
plt.axis('off')
plt.show()`,

                'edge-detection': `import cv2
import numpy as np

# تحميل الصورة وتحويلها إلى رمادي
image = cv2.imread('image.jpg')
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# تطبيق كشف الحواف باستخدام Canny
edges = cv2.Canny(gray, 100, 200)

# عرض النتيجة
cv2.imshow('الحواف', edges)
cv2.waitKey(0)
cv2.destroyAllWindows()`,

                'color-conversion': `import cv2

# تحميل الصورة
image = cv2.imread('image.jpg')

# تحويل من BGR إلى HSV
hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

# تحويل من BGR إلى RGB
rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

print("تم تحويل الصورة بنجاح!")`,

                'histogram': `import cv2
import matplotlib.pyplot as plt

# تحميل الصورة
image = cv2.imread('image.jpg')
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# حساب الهيستوجرام
hist = cv2.calcHist([gray], [0], None, [256], [0, 256])

# رسم الهيستوجرام
plt.plot(hist)
plt.title('هيستوجرام الصورة')
plt.xlabel('شدة البكسل')
plt.ylabel('عدد البكسلات')
plt.show()`
            };

            if (templates[template]) {
                editor.value = templates[template];
            }
        }

        function runCode() {
            const code = document.getElementById('code-editor').value;
            const output = document.getElementById('code-output');

            // Simulate code execution (in a real implementation, you'd send this to a backend)
            output.innerHTML = `<div class="text-green-600">تم تشغيل الكود بنجاح!</div>
<div class="mt-2 text-gray-600">ملاحظة: هذا محاكي للكود. في التطبيق الحقيقي، سيتم تشغيل الكود على الخادم.</div>
<div class="mt-2 bg-gray-100 p-2 rounded"><pre>${code}</pre></div>`;
        }

        // === Concept Map Functions ===

        function initConceptMap() {
            updateProgress('tools', 'concept-map');
        }

        async function generateConceptMap() {
            const container = document.getElementById('concept-map-container');
            container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-600"></div></div>';

            try {
                const prompt = "أنشئ خريطة مفاهيمية للرؤية الحاسوبية تتضمن المفاهيم الأساسية والعلاقات بينها";
                const payload = {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "OBJECT",
                            properties: {
                                nodes: {
                                    type: "ARRAY",
                                    items: {
                                        type: "OBJECT",
                                        properties: {
                                            id: { type: "STRING" },
                                            label: { type: "STRING" },
                                            x: { type: "NUMBER" },
                                            y: { type: "NUMBER" }
                                        }
                                    }
                                },
                                connections: {
                                    type: "ARRAY",
                                    items: {
                                        type: "OBJECT",
                                        properties: {
                                            from: { type: "STRING" },
                                            to: { type: "STRING" },
                                            label: { type: "STRING" }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };

                const response = await callGeminiApi(payload);
                const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;

                if (text) {
                    const mapData = JSON.parse(text);
                    renderConceptMap(mapData);
                } else {
                    throw new Error('No response from AI');
                }
            } catch (error) {
                console.error('Error generating concept map:', error);
                container.innerHTML = `
                    <div class="p-4">
                        <div class="text-red-500 mb-4">حدث خطأ في إنشاء الخريطة. إليك خريطة مفاهيمية أساسية:</div>
                        <div class="concept-map-demo">
                            <div class="concept-node" style="position: absolute; top: 50px; left: 200px; background: #0891b2; color: white; padding: 10px; border-radius: 8px;">الرؤية الحاسوبية</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 50px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">معالجة الصور</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 200px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">التعلم العميق</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 350px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">الرؤية ثلاثية الأبعاد</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 100px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">المرشحات</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 200px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">CNN</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 300px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">التجسيم</div>
                        </div>
                    </div>
                `;
            }
        }

        function renderConceptMap(mapData) {
            const container = document.getElementById('concept-map-container');
            let html = '<div class="relative w-full h-full">';

            // Render nodes
            mapData.nodes.forEach(node => {
                html += `<div class="concept-node absolute bg-cyan-600 text-white p-2 rounded shadow"
                         style="left: ${node.x}px; top: ${node.y}px;">${node.label}</div>`;
            });

            // Render connections (simplified as lines)
            mapData.connections.forEach(conn => {
                const fromNode = mapData.nodes.find(n => n.id === conn.from);
                const toNode = mapData.nodes.find(n => n.id === conn.to);
                if (fromNode && toNode) {
                    html += `<div class="connection-line absolute border-t-2 border-gray-400"
                             style="left: ${fromNode.x + 50}px; top: ${fromNode.y + 20}px;
                             width: ${Math.abs(toNode.x - fromNode.x)}px;"></div>`;
                }
            });

            html += '</div>';
            container.innerHTML = html;
        }

        function resetConceptMap() {
            const container = document.getElementById('concept-map-container');
            container.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">انقر على "إنشاء خريطة جديدة" لبدء إنشاء خريطة المفاهيم</div>';
        }

        // Make functions globally available
        window.openImageProcessor = openImageProcessor;
        window.open3DVisualization = open3DVisualization;
        window.openAlgorithmComparison = openAlgorithmComparison;
        window.openProgressTracker = openProgressTracker;
        window.openCodeEditor = openCodeEditor;
        window.openConceptMap = openConceptMap;
        window.closeToolModal = closeToolModal;
        window.applyFilter = applyFilter;
        window.resetImage = resetImage;
        window.update3DScene = update3DScene;
        window.reset3DScene = reset3DScene;
        window.updateAlgorithmComparison = updateAlgorithmComparison;
        window.resetProgress = resetProgress;
        window.loadCodeTemplate = loadCodeTemplate;
        window.runCode = runCode;
        window.generateConceptMap = generateConceptMap;
        window.resetConceptMap = resetConceptMap;

        const evaluationChartElement = document.getElementById('evaluationChart');
        if (evaluationChartElement) {
            const ctx = evaluationChartElement.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['امتحان فصلي (60%)', 'تقييم مستمر (40%)'],
                    datasets: [{
                        data: [60, 40],
                        backgroundColor: ['#0891b2', '#f97316'],
                        borderColor: '#f3f4f6',
                        borderWidth: 4,
                        hoverOffset: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: { 
                        legend: { position: 'bottom', labels: { padding: 20, font: { family: "'Cairo', sans-serif", size: 14 } } },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

        const menuBtn = document.getElementById('menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        if(menuBtn && mobileMenu) {
            menuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        }
    });
    </script>
</body>
</html>
