<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرؤية الحاسوبية - المادة التعليمية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- Performance optimized: Load libraries asynchronously -->
    <script>
        // Preload critical resources
        const preloadResources = [
            'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js'
        ];

        preloadResources.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'script';
            link.href = url;
            document.head.appendChild(link);
        });
    </script>

    <!-- Load libraries with defer for better performance -->
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>

    <!-- Optimized CSS loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    </noscript>
</head>
<body class="bg-gray-100 text-gray-800">
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>

    <!-- Enhanced Header with Accessibility and RTL Support -->
    <header class="bg-white shadow-md sticky top-0 z-50 transition-all duration-300" role="banner">
        <nav class="container mx-auto px-6 py-4" role="navigation" aria-label="التنقل الرئيسي">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-cyan-700 transition-colors duration-300 hover:text-cyan-800">
                    الرؤية الحاسوبية
                </h1>

                <!-- Desktop Navigation with improved RTL spacing -->
                <div class="hidden md:flex space-x-reverse space-x-8" role="menubar">
                    <a href="#home" class="nav-link text-gray-600 hover:text-cyan-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50" role="menuitem" tabindex="0" aria-label="الانتقال إلى الصفحة الرئيسية">الرئيسية</a>
                    <a href="#objectifs" class="nav-link text-gray-600 hover:text-cyan-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50" role="menuitem" tabindex="0" aria-label="الانتقال إلى الأهداف">الأهداف</a>
                    <a href="#contenu" class="nav-link text-gray-600 hover:text-cyan-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50" role="menuitem" tabindex="0" aria-label="الانتقال إلى المحتوى">المحتوى</a>
                    <a href="#tools" class="nav-link text-gray-600 hover:text-cyan-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50" role="menuitem" tabindex="0" aria-label="الانتقال إلى الأدوات التفاعلية">الأدوات التفاعلية</a>
                    <a href="#evaluation" class="nav-link text-gray-600 hover:text-cyan-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-cyan-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50" role="menuitem" tabindex="0" aria-label="الانتقال إلى التقييم">التقييم</a>
                </div>

                <!-- Mobile Menu Button with Accessibility -->
                <div class="md:hidden">
                    <button id="menu-btn"
                            class="text-gray-600 hover:text-cyan-600 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50 p-2 rounded-lg transition-all duration-300"
                            aria-label="فتح القائمة الرئيسية"
                            aria-expanded="false"
                            aria-controls="mobile-menu"
                            type="button">
                        <svg class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                        <span class="sr-only">فتح القائمة</span>
                    </button>
                </div>
            </div>

            <!-- Enhanced Mobile Menu with Animation -->
            <div id="mobile-menu"
                 class="hidden md:hidden mt-3 bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 transform origin-top"
                 role="menu"
                 aria-labelledby="menu-btn">
                <div class="py-2">
                    <a href="#home" class="mobile-nav-link block py-3 px-4 text-gray-600 hover:text-cyan-600 hover:bg-cyan-50 transition-all duration-300 border-r-4 border-transparent hover:border-cyan-500" role="menuitem" tabindex="-1">الرئيسية</a>
                    <a href="#objectifs" class="mobile-nav-link block py-3 px-4 text-gray-600 hover:text-cyan-600 hover:bg-cyan-50 transition-all duration-300 border-r-4 border-transparent hover:border-cyan-500" role="menuitem" tabindex="-1">الأهداف</a>
                    <a href="#contenu" class="mobile-nav-link block py-3 px-4 text-gray-600 hover:text-cyan-600 hover:bg-cyan-50 transition-all duration-300 border-r-4 border-transparent hover:border-cyan-500" role="menuitem" tabindex="-1">المحتوى</a>
                    <a href="#tools" class="mobile-nav-link block py-3 px-4 text-gray-600 hover:text-cyan-600 hover:bg-cyan-50 transition-all duration-300 border-r-4 border-transparent hover:border-cyan-500" role="menuitem" tabindex="-1">الأدوات التفاعلية</a>
                    <a href="#evaluation" class="mobile-nav-link block py-3 px-4 text-gray-600 hover:text-cyan-600 hover:bg-cyan-50 transition-all duration-300 border-r-4 border-transparent hover:border-cyan-500" role="menuitem" tabindex="-1">التقييم</a>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container mx-auto px-6 py-12" role="main">

        <section id="home" class="text-center mb-20">
            <h2 class="text-4xl font-bold text-cyan-800 mb-4">ماستر ذكاء اصطناعي</h2>
            <p class="text-lg max-w-3xl mx-auto text-gray-700">
                تهدف هذه المادة إلى إعطاء نظرة شاملة عن مجال الرؤية الحاسوبية، بدءًا من تكوين الصور وهندستها، مرورًا بالقياس الضوئي والرقمنة، وانتهاءً بإسقاط المشاهد ثلاثية الأبعاد على متن الصورة. هذه المنصة التفاعلية مصممة لتكون دليلك لاستكشاف هذا المجال المثير.
            </p>
        </section>

        <section id="objectifs" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">أهداف المادة</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto">تهدف المادة إلى تزويدك بفهم عميق للرؤية الاصطناعية، تغطي الجوانب النظرية والتطبيقية الأساسية لإتقان هذا المجال.</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold mb-2">المفاهيم الأساسية</h3>
                    <p class="text-gray-600">تقديم المفاهيم الأساسية للإدراك البصري الاصطناعي.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🧠</div>
                    <h3 class="text-xl font-semibold mb-2">الأساليب الحديثة</h3>
                    <p class="text-gray-600">فهم الطرق الكلاسيكية والحديثة لمعالجة وتحليل الصور والفيديو.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-semibold mb-2">إتقان التعلم العميق</h3>
                    <p class="text-gray-600">إتقان تقنيات التعلم العميق المتقدمة مثل CNNs و Vision Transformers.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">💡</div>
                    <h3 class="text-xl font-semibold mb-2">الاتجاهات الجديدة</h3>
                    <p class="text-gray-600">اكتشاف الاتجاهات الجديدة مثل التعلم الذاتي والنماذج متعددة الوسائط.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">🛠️</div>
                    <h3 class="text-xl font-semibold mb-2">المهارات العملية</h3>
                    <p class="text-gray-600">تطوير خبرة عملية مع أدوات مثل OpenCV, PyTorch, و TensorFlow.</p>
                </div>
                <div class="section-card p-6 text-center">
                    <div class="text-4xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold mb-2">المعرفة المسبقة</h3>
                    <p class="text-gray-600">يوصى بمعرفة مسبقة في الجبر الخطي، الاحتمالات، والبرمجة بلغة بايثون.</p>
                </div>
            </div>
        </section>

        <!-- Module 3, 4, 5: Course Content (Tabs) Placeholder -->
        <section id="contenu" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">محتوى المادة</h2>
                <!-- Tab buttons will be generated here by script.js -->
                <div id="tabs-container" class="flex flex-wrap justify-center gap-2 mb-8"></div>
                <!-- Tab content will be displayed here -->
                <div id="tab-content-container" class="bg-white p-8 rounded-lg shadow-lg min-h-[250px]"></div>
            </div>
        </section>

        <!-- Enhanced Interactive Educational Tools Section -->
        <section id="tools" class="mb-20" role="region" aria-labelledby="tools-heading">
            <div class="text-center mb-12">
                <h2 id="tools-heading" class="text-3xl font-bold text-cyan-800 mb-4">الأدوات التعليمية التفاعلية</h2>
                <p class="text-md text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    مجموعة من الأدوات التفاعلية المتقدمة لتعزيز تجربة التعلم وفهم مفاهيم الرؤية الحاسوبية بشكل عملي وتفاعلي.
                    <br><span class="text-sm text-gray-500 mt-2 block">انقر على أي أداة لبدء التجربة التفاعلية</span>
                </p>
            </div>

            <!-- Enhanced Tools Grid with Loading States -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12" role="grid" aria-label="شبكة الأدوات التفاعلية">

                <!-- Image Processing Simulator -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="image-processor"
                     tabindex="0"
                     aria-describedby="image-processor-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-cyan-50 to-blue-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة معالجة الصور">🖼️</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-cyan-700 transition-colors duration-300">محاكي معالجة الصور</h3>
                        <p id="image-processor-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            جرب تطبيق المرشحات والتحويلات على الصور مباشرة. يدعم التحويل إلى الرمادي، كشف الحواف، والتشويش.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="openImageProcessor()"
                                    class="tool-btn bg-cyan-600 text-white px-6 py-3 rounded-lg hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح محاكي معالجة الصور"
                                    data-tooltip="ابدأ بتحميل صورة وجرب المرشحات المختلفة">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 3D Visualization -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="3d-visualization"
                     tabindex="0"
                     aria-describedby="3d-viz-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة التصور ثلاثي الأبعاد">🎯</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-purple-700 transition-colors duration-300">التصور ثلاثي الأبعاد</h3>
                        <p id="3d-viz-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            استكشف المفاهيم الهندسية والتحويلات ثلاثية الأبعاد. تحكم في الدوران والتكبير بشكل تفاعلي.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="open3DVisualization()"
                                    class="tool-btn bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح أداة التصور ثلاثي الأبعاد"
                                    data-tooltip="تعلم الهندسة ثلاثية الأبعاد بشكل تفاعلي">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Algorithm Comparison -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="algorithm-comparison"
                     tabindex="0"
                     aria-describedby="algo-comp-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-green-50 to-emerald-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة مقارنة الخوارزميات">⚖️</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-green-700 transition-colors duration-300">مقارنة الخوارزميات</h3>
                        <p id="algo-comp-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            قارن بين أداء الخوارزميات المختلفة بصريًا. يشمل CNN، Vision Transformers، وخوارزميات كلاسيكية.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="openAlgorithmComparison()"
                                    class="tool-btn bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح أداة مقارنة الخوارزميات"
                                    data-tooltip="اكتشف الفروق بين الخوارزميات المختلفة">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Progress Tracker -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="progress-tracker"
                     tabindex="0"
                     aria-describedby="progress-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-50 to-red-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة متتبع التقدم">📊</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-orange-700 transition-colors duration-300">متتبع التقدم</h3>
                        <p id="progress-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            تتبع تقدمك التعليمي وإنجازاتك في المادة. احصل على شارات الإنجاز وراقب أداءك.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="openProgressTracker()"
                                    class="tool-btn bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح متتبع التقدم"
                                    data-tooltip="راقب تقدمك واحصل على الإنجازات">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Code Editor -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="code-editor"
                     tabindex="0"
                     aria-describedby="code-editor-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-slate-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة محرر الكود">💻</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-gray-700 transition-colors duration-300">محرر الكود التفاعلي</h3>
                        <p id="code-editor-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            اكتب وجرب كود Python للرؤية الحاسوبية مباشرة. يتضمن قوالب جاهزة وأمثلة عملية.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="openCodeEditor()"
                                    class="tool-btn bg-gray-700 text-white px-6 py-3 rounded-lg hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح محرر الكود التفاعلي"
                                    data-tooltip="اكتب وجرب كود Python مباشرة">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Concept Map Generator -->
                <div class="tool-card section-card p-6 text-center group relative overflow-hidden"
                     role="gridcell"
                     data-tool="concept-map"
                     tabindex="0"
                     aria-describedby="concept-map-desc">
                    <div class="absolute inset-0 bg-gradient-to-br from-teal-50 to-cyan-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300" role="img" aria-label="أيقونة مولد الخرائط المفاهيمية">🗺️</div>
                        <h3 class="text-xl font-semibold mb-2 text-gray-800 group-hover:text-teal-700 transition-colors duration-300">مولد الخرائط المفاهيمية</h3>
                        <p id="concept-map-desc" class="text-gray-600 mb-4 text-sm leading-relaxed">
                            أنشئ خرائط مفاهيمية تفاعلية للمواضيع. يستخدم الذكاء الاصطناعي لربط المفاهيم.
                        </p>
                        <div class="tooltip-container relative">
                            <button onclick="openConceptMap()"
                                    class="tool-btn bg-teal-600 text-white px-6 py-3 rounded-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                                    aria-label="فتح مولد الخرائط المفاهيمية"
                                    data-tooltip="أنشئ خرائط مفاهيمية ذكية">
                                <span class="flex items-center justify-center">
                                    <span>تجربة الأداة</span>
                                    <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Tool Modal Container with Accessibility -->
            <div id="tool-modal"
                 class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden z-50 flex items-center justify-center p-4 transition-all duration-300 opacity-0"
                 role="dialog"
                 aria-modal="true"
                 aria-labelledby="modal-title"
                 aria-describedby="modal-content"
                 tabindex="-1">

                <!-- Modal Backdrop -->
                <div class="absolute inset-0" onclick="closeToolModal()" aria-label="إغلاق النافذة المنبثقة"></div>

                <!-- Modal Content -->
                <div class="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl transform scale-95 transition-all duration-300 relative z-10"
                     id="modal-dialog">

                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-cyan-50 to-blue-50">
                        <div class="flex items-center">
                            <div id="modal-icon" class="text-2xl ml-3"></div>
                            <h3 id="modal-title" class="text-2xl font-bold text-cyan-800 flex-1"></h3>
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- Fullscreen Toggle -->
                            <button id="fullscreen-btn"
                                    onclick="toggleFullscreen()"
                                    class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                                    aria-label="تبديل وضع ملء الشاشة"
                                    title="ملء الشاشة">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                </svg>
                            </button>

                            <!-- Close Button -->
                            <button onclick="closeToolModal()"
                                    class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                                    aria-label="إغلاق النافذة المنبثقة"
                                    title="إغلاق">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Modal Body with Loading State -->
                    <div class="relative">
                        <!-- Loading Overlay -->
                        <div id="modal-loading" class="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-20 hidden">
                            <div class="flex flex-col items-center">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mb-4"></div>
                                <p class="text-gray-600">جاري تحميل الأداة...</p>
                            </div>
                        </div>

                        <!-- Scrollable Content -->
                        <div id="modal-content"
                             class="p-6 overflow-y-auto max-h-[calc(90vh-120px)] custom-scrollbar"
                             tabindex="0">
                            <!-- Tool content will be loaded here -->
                        </div>
                    </div>

                    <!-- Modal Footer (Optional) -->
                    <div id="modal-footer" class="hidden border-t border-gray-200 p-4 bg-gray-50 flex justify-between items-center">
                        <div class="text-sm text-gray-500" id="modal-help-text"></div>
                        <div class="flex space-x-2" id="modal-actions">
                            <!-- Action buttons will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Module 6: Evaluation Section Placeholder -->
        <section id="evaluation" class="mb-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-cyan-800 mb-4">التقييم والمراجع</h2>
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="section-card p-6">
                        <h3 class="text-2xl font-semibold mb-4 text-center">طريقة التقييم</h3>
                        <div class="max-w-[350px] mx-auto h-[350px]">
                            <canvas id="evaluationChart"></canvas>
                        </div>
                    </div>
                    <div class="section-card p-8">
                        <h3 class="text-2xl font-semibold mb-6">المراجع الببليوغرافية</h3>
                        <ul class="space-y-6 text-right">
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: Algorithms and Applications (2nd ed.)</p>
                                <p class="text-sm text-gray-500">Szeliski, R. (2022). Springer.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Computer Vision: A Modern Approach (3rd ed.)</p>
                                <p class="text-sm text-gray-500">Forsyth, D. A. (2023). Pearson.</p>
                            </li>
                            <li class="border-r-4 border-cyan-500 pr-4">
                                <p class="font-bold">Deep Learning (1st ed.)</p>
                                <p class="text-sm text-gray-500">Goodfellow, I., Bengio, Y., & Courville, A. (2016). MIT Press.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Module 1: Footer Placeholder -->
    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-6 text-center">
            <p>&copy; 2025 الرؤية الحاسوبية - ماستر ذكاء اصطناعي</p>
        </div>
    </footer>

    <script>
    // --------------------------------------------------------------------------
    // !! تحذير أمني: لا تضع مفتاح API الخاص بك هنا مباشرة في بيئة الإنتاج !!
    // !! هذا لأغراض العرض فقط. في تطبيق حقيقي، يجب حماية المفتاح.   !!
    // --------------------------------------------------------------------------
    const API_KEY = "AIzaSyBF9TW-VQhxMXRbEIt4pLX8LaWHRp2aKhk"; // <-- ضع مفتاح API الجديد والآمن هنا

    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-preview-0514:generateContent?key=${API_KEY}`;

    // Enhanced Global State Management
    const AppState = {
        currentTool: null,
        isModalOpen: false,
        isFullscreen: false,
        progressData: null,
        loadedLibraries: new Set(),

        // Initialize state
        init() {
            try {
                this.progressData = JSON.parse(localStorage.getItem('cvProgress') || '{}');
                // Convert arrays back to Sets for tools and chapters
                if (this.progressData.tools && Array.isArray(this.progressData.tools)) {
                    this.progressData.tools = new Set(this.progressData.tools);
                }
                if (this.progressData.chapters && Array.isArray(this.progressData.chapters)) {
                    this.progressData.chapters = new Set(this.progressData.chapters);
                }
            } catch (error) {
                console.warn('Failed to load progress data:', error);
                this.progressData = {};
            }
        }
    };

    // Performance: Debounce function for expensive operations
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Performance: Throttle function for frequent events
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    // Enhanced Error Handler with User-Friendly Messages
    class ErrorHandler {
        static handle(error, context = 'عام', userMessage = null) {
            console.error(`[${context}] Error:`, error);

            const defaultMessages = {
                'network': 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من اتصالك.',
                'api': 'خطأ في الخدمة. يرجى المحاولة مرة أخرى لاحقاً.',
                'storage': 'مشكلة في حفظ البيانات. تأكد من توفر مساحة كافية.',
                'library': 'فشل في تحميل المكتبة المطلوبة. يرجى إعادة تحميل الصفحة.',
                'general': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
            };

            const message = userMessage || defaultMessages[context] || defaultMessages.general;
            this.showUserError(message);

            // Send error to analytics (if available)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'exception', {
                    description: `${context}: ${error.message}`,
                    fatal: false
                });
            }
        }

        static showUserError(message) {
            // Create or update error notification
            let errorDiv = document.getElementById('error-notification');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'error-notification';
                errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
                errorDiv.setAttribute('role', 'alert');
                errorDiv.setAttribute('aria-live', 'assertive');
                document.body.appendChild(errorDiv);
            }

            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 hover:bg-red-600 rounded p-1">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;

            // Show notification
            setTimeout(() => errorDiv.classList.remove('translate-x-full'), 100);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentElement) {
                    errorDiv.classList.add('translate-x-full');
                    setTimeout(() => errorDiv.remove(), 300);
                }
            }, 5000);
        }
    }

    // Library Loading Manager
    class LibraryLoader {
        static async loadLibrary(name, url, checkFunction) {
            if (AppState.loadedLibraries.has(name)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                // Check if library is already loaded
                if (checkFunction && checkFunction()) {
                    AppState.loadedLibraries.add(name);
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = url;
                script.onload = () => {
                    AppState.loadedLibraries.add(name);
                    resolve();
                };
                script.onerror = () => {
                    ErrorHandler.handle(new Error(`Failed to load ${name}`), 'library');
                    reject(new Error(`Failed to load ${name}`));
                };
                document.head.appendChild(script);
            });
        }

        static async ensureLibraries(libraries) {
            const loadPromises = libraries.map(lib =>
                this.loadLibrary(lib.name, lib.url, lib.check)
            );

            try {
                await Promise.all(loadPromises);
                return true;
            } catch (error) {
                ErrorHandler.handle(error, 'library');
                return false;
            }
        }
    }

    /**
     * Enhanced Gemini API call with better error handling and retry logic
     * @param {object} payload - البيانات التي سيتم إرسالها إلى API
     * @param {number} retries - عدد المحاولات المتبقية
     * @returns {Promise<object>} - الرد من API
     */
    async function callGeminiApi(payload, retries = 2) {
        const geminiContentArea = document.getElementById('gemini-content-area');

        // Show loading state
        if (geminiContentArea) {
            geminiContentArea.innerHTML = `
                <div class="flex items-center justify-center space-x-2 p-4">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-600"></div>
                    <p class="text-gray-600">جاري التفكير...</p>
                </div>
            `;
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

            const response = await fetch(API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "User-Agent": "CV-Educational-Platform/1.0"
                },
                body: JSON.stringify(payload),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API call failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();

            // Validate response structure
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Invalid API response structure');
            }

            return data;

        } catch (error) {
            console.error('Gemini API Error:', error);

            // Retry logic for network errors
            if (retries > 0 && (error.name === 'AbortError' || error.message.includes('fetch'))) {
                console.log(`Retrying API call... (${retries} attempts left)`);
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                return callGeminiApi(payload, retries - 1);
            }

            // Show user-friendly error
            if (geminiContentArea) {
                const errorMessage = error.name === 'AbortError'
                    ? 'انتهت مهلة الاستجابة. يرجى المحاولة مرة أخرى.'
                    : 'حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى.';

                geminiContentArea.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-500 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <p class="text-red-700">${errorMessage}</p>
                        </div>
                        <button onclick="location.reload()" class="mt-2 bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm transition-colors">
                            إعادة تحميل الصفحة
                        </button>
                    </div>
                `;
            }

            ErrorHandler.handle(error, 'api');
            throw error;
        }
    }

    // === Enhanced Interactive Tools Functions ===

    // Modal Management with Accessibility
    class ModalManager {
        static focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        static previousFocus = null;

        static openModal(title, content, icon = '🔧') {
            try {
                // Store current focus
                this.previousFocus = document.activeElement;

                const modal = document.getElementById('tool-modal');
                const modalTitle = document.getElementById('modal-title');
                const modalIcon = document.getElementById('modal-icon');
                const modalContent = document.getElementById('modal-content');
                const modalLoading = document.getElementById('modal-loading');

                if (!modal || !modalTitle || !modalContent) {
                    throw new Error('Modal elements not found');
                }

                // Set content
                modalTitle.textContent = title;
                modalIcon.textContent = icon;
                modalContent.innerHTML = content;

                // Show modal with animation
                modal.classList.remove('hidden');
                modal.classList.add('show');

                // Prevent body scroll
                document.body.style.overflow = 'hidden';
                document.body.style.paddingRight = this.getScrollbarWidth() + 'px';

                // Set focus to modal
                setTimeout(() => {
                    const firstFocusable = modal.querySelector(this.focusableElements);
                    if (firstFocusable) {
                        firstFocusable.focus();
                    } else {
                        modalContent.focus();
                    }
                }, 100);

                // Add keyboard event listeners
                document.addEventListener('keydown', this.handleKeyDown);

                AppState.isModalOpen = true;

                // Announce to screen readers
                this.announceToScreenReader(`تم فتح ${title}`);

            } catch (error) {
                ErrorHandler.handle(error, 'modal', 'فشل في فتح النافذة المنبثقة');
            }
        }

        static closeModal() {
            try {
                const modal = document.getElementById('tool-modal');
                if (!modal) return;

                // Hide modal with animation
                modal.classList.remove('show');

                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);

                // Restore body scroll
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';

                // Restore focus
                if (this.previousFocus) {
                    this.previousFocus.focus();
                    this.previousFocus = null;
                }

                // Remove event listeners
                document.removeEventListener('keydown', this.handleKeyDown);

                // Cleanup current tool
                if (AppState.currentTool && AppState.currentTool.cleanup) {
                    AppState.currentTool.cleanup();
                }
                AppState.currentTool = null;
                AppState.isModalOpen = false;

                this.announceToScreenReader('تم إغلاق النافذة المنبثقة');

            } catch (error) {
                ErrorHandler.handle(error, 'modal', 'فشل في إغلاق النافذة المنبثقة');
            }
        }

        static handleKeyDown(event) {
            if (!AppState.isModalOpen) return;

            const modal = document.getElementById('tool-modal');
            if (!modal) return;

            // Close on Escape
            if (event.key === 'Escape') {
                event.preventDefault();
                ModalManager.closeModal();
                return;
            }

            // Trap focus within modal
            if (event.key === 'Tab') {
                const focusableElements = modal.querySelectorAll(ModalManager.focusableElements);
                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                if (event.shiftKey) {
                    if (document.activeElement === firstElement) {
                        event.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        event.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        }

        static getScrollbarWidth() {
            const outer = document.createElement('div');
            outer.style.visibility = 'hidden';
            outer.style.overflow = 'scroll';
            outer.style.msOverflowStyle = 'scrollbar';
            document.body.appendChild(outer);

            const inner = document.createElement('div');
            outer.appendChild(inner);

            const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
            outer.parentNode.removeChild(outer);

            return scrollbarWidth;
        }

        static showLoading() {
            const loading = document.getElementById('modal-loading');
            if (loading) {
                loading.classList.remove('hidden');
            }
        }

        static hideLoading() {
            const loading = document.getElementById('modal-loading');
            if (loading) {
                loading.classList.add('hidden');
            }
        }

        static announceToScreenReader(message) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.className = 'sr-only';
            announcement.textContent = message;
            document.body.appendChild(announcement);

            setTimeout(() => {
                if (announcement.parentNode) {
                    announcement.parentNode.removeChild(announcement);
                }
            }, 1000);
        }
    }

    // Fullscreen functionality
    function toggleFullscreen() {
        const modal = document.getElementById('modal-dialog');
        if (!modal) return;

        if (!AppState.isFullscreen) {
            modal.classList.add('fixed', 'inset-0', 'max-w-none', 'max-h-none', 'rounded-none');
            AppState.isFullscreen = true;
        } else {
            modal.classList.remove('fixed', 'inset-0', 'max-w-none', 'max-h-none', 'rounded-none');
            AppState.isFullscreen = false;
        }
    }

    // Legacy function wrappers for backward compatibility
    function openToolModal(title, content, icon) {
        ModalManager.openModal(title, content, icon);
    }

    function closeToolModal() {
        ModalManager.closeModal();
    }

    async function openImageProcessor() {
        try {
            ModalManager.showLoading();

            const content = `
                <div class="grid md:grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-lg font-semibold mb-4 flex items-center">
                                <svg class="w-5 h-5 ml-2 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                تحميل الصورة
                            </h4>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-cyan-400 transition-colors">
                                <input type="file" id="imageInput" accept="image/*" class="hidden" aria-label="اختر صورة للمعالجة">
                                <label for="imageInput" class="cursor-pointer">
                                    <div class="text-gray-400 mb-2">
                                        <svg class="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </div>
                                    <p class="text-sm text-gray-600">انقر لاختيار صورة أو اسحبها هنا</p>
                                    <p class="text-xs text-gray-400 mt-1">PNG, JPG, GIF حتى 10MB</p>
                                </label>
                            </div>
                        </div>

                        <div id="original-image-container" class="hidden">
                            <h5 class="font-medium mb-2">الصورة الأصلية</h5>
                            <div class="border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
                                <canvas id="originalCanvas" class="max-w-full h-auto"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <h4 class="text-lg font-semibold mb-4 flex items-center">
                                <svg class="w-5 h-5 ml-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                </svg>
                                المرشحات والتأثيرات
                            </h4>
                            <div class="grid grid-cols-2 gap-2 mb-4" id="filter-buttons">
                                <button onclick="applyFilter('grayscale')"
                                        class="filter-btn bg-gray-100 hover:bg-gray-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="تحويل الصورة إلى اللون الرمادي">
                                    🔘 رمادي
                                </button>
                                <button onclick="applyFilter('blur')"
                                        class="filter-btn bg-blue-100 hover:bg-blue-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="تطبيق تأثير التشويش">
                                    🌫️ تشويش
                                </button>
                                <button onclick="applyFilter('edge')"
                                        class="filter-btn bg-green-100 hover:bg-green-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="كشف حواف الكائنات في الصورة">
                                    ⚡ كشف الحواف
                                </button>
                                <button onclick="applyFilter('brightness')"
                                        class="filter-btn bg-yellow-100 hover:bg-yellow-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="زيادة سطوع الصورة">
                                    ☀️ سطوع
                                </button>
                                <button onclick="applyFilter('contrast')"
                                        class="filter-btn bg-purple-100 hover:bg-purple-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="زيادة التباين">
                                    🎨 تباين
                                </button>
                                <button onclick="resetImage()"
                                        class="filter-btn bg-red-100 hover:bg-red-200 px-4 py-3 rounded-lg transition-all duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled
                                        data-tooltip="إعادة الصورة إلى حالتها الأصلية">
                                    🔄 إعادة تعيين
                                </button>
                            </div>
                        </div>

                        <div id="processed-image-container" class="hidden">
                            <h5 class="font-medium mb-2">الصورة المعالجة</h5>
                            <div class="border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
                                <canvas id="processedCanvas" class="max-w-full h-auto"></canvas>
                            </div>
                            <div class="mt-2 flex justify-between text-xs text-gray-500">
                                <span id="processing-time"></span>
                                <button onclick="downloadProcessedImage()" class="text-cyan-600 hover:text-cyan-700 font-medium">
                                    تحميل الصورة المعالجة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="mt-6 bg-cyan-50 border border-cyan-200 rounded-lg p-4">
                    <h5 class="font-medium text-cyan-800 mb-2">💡 نصائح الاستخدام:</h5>
                    <ul class="text-sm text-cyan-700 space-y-1">
                        <li>• اختر صورة بحجم مناسب للحصول على أفضل أداء</li>
                        <li>• جرب المرشحات المختلفة لفهم تأثير كل منها</li>
                        <li>• استخدم "إعادة تعيين" للعودة إلى الصورة الأصلية</li>
                        <li>• يمكنك تحميل الصورة المعالجة بعد تطبيق المرشح</li>
                    </ul>
                </div>
            `;

            ModalManager.openModal('محاكي معالجة الصور', content, '🖼️');
            ModalManager.hideLoading();

            // Initialize after modal is shown
            setTimeout(() => {
                initImageProcessor();
            }, 100);

        } catch (error) {
            ModalManager.hideLoading();
            ErrorHandler.handle(error, 'image-processor', 'فشل في فتح محاكي معالجة الصور');
        }
    }

    function open3DVisualization() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">التحكم في المشهد ثلاثي الأبعاد</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران X:</label>
                            <input type="range" id="rotationX" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران Y:</label>
                            <input type="range" id="rotationY" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">دوران Z:</label>
                            <input type="range" id="rotationZ" min="0" max="360" value="0" class="w-full" onchange="update3DScene()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">التكبير:</label>
                            <input type="range" id="zoom" min="1" max="10" value="5" step="0.1" class="w-full" onchange="update3DScene()">
                        </div>
                        <button onclick="reset3DScene()" class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded w-full">إعادة تعيين</button>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">المشهد ثلاثي الأبعاد</h4>
                    <div id="threejs-container" class="border border-gray-300 bg-gray-100" style="width: 100%; height: 400px;"></div>
                </div>
            </div>
        `;
        openToolModal('التصور ثلاثي الأبعاد', content);
        init3DVisualization();
    }

    function openAlgorithmComparison() {
        const content = `
            <div class="space-y-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold mb-4">اختر الخوارزميات للمقارنة</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" value="cnn" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>الشبكات العصبونية الالتفافية (CNN)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="vit" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>محولات الرؤية (Vision Transformers)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="svm" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>آلة الدعم الشعاعي (SVM)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="knn" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>أقرب الجيران (K-NN)</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">معايير المقارنة</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" value="accuracy" class="ml-2" checked onchange="updateAlgorithmComparison()">
                                <span>الدقة</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="speed" class="ml-2" checked onchange="updateAlgorithmComparison()">
                                <span>السرعة</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="memory" class="ml-2" onchange="updateAlgorithmComparison()">
                                <span>استهلاك الذاكرة</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div id="comparison-chart-container">
                    <canvas id="comparisonChart" width="400" height="200"></canvas>
                </div>
            </div>
        `;
        openToolModal('مقارنة الخوارزميات', content);
        initAlgorithmComparison();
    }

    /**
     * يعرض نصًا منسقًا في منطقة المحتوى
     * @param {string} title - عنوان المحتوى
     * @param {string} text - النص المراد عرضه
     */
    function displayTextResponse(title, text) {
        const geminiContentArea = document.getElementById('gemini-content-area');
        if (geminiContentArea) {
            // استبدال علامات النجمة لإنشاء قوائم HTML
            const formattedText = text.replace(/\*\s(.*?)(?=\n\*|\n$)/g, '<li>$1</li>').replace(/\*\*/g, '<strong>').replace(/\* /g, '<li>');
            geminiContentArea.innerHTML = `<h4 class="font-bold text-lg mb-2">${title}</h4><ul class="list-disc list-inside space-y-2">${formattedText}</ul>`;
        }
    }

    function openProgressTracker() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">إحصائيات التقدم</h4>
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-2">
                                <span>الفصول المكتملة</span>
                                <span id="completed-chapters">0/9</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="progress-bar" class="bg-cyan-600 h-2 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span>الاختبارات المكتملة</span>
                                <span id="completed-quizzes">0</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span>الأدوات المستخدمة</span>
                                <span id="tools-used">0/6</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الإنجازات</h4>
                    <div id="achievements-list" class="space-y-2">
                        <!-- Achievements will be populated here -->
                    </div>
                    <button onclick="resetProgress()" class="mt-4 bg-red-200 hover:bg-red-300 px-4 py-2 rounded w-full">إعادة تعيين التقدم</button>
                </div>
            </div>
        `;
        openToolModal('متتبع التقدم', content);
        initProgressTracker();
    }

    function openCodeEditor() {
        const content = `
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-4">محرر الكود</h4>
                    <div class="mb-4">
                        <select id="code-template" onchange="loadCodeTemplate()" class="w-full p-2 border border-gray-300 rounded">
                            <option value="">اختر قالب كود...</option>
                            <option value="image-loading">تحميل وعرض صورة</option>
                            <option value="edge-detection">كشف الحواف</option>
                            <option value="color-conversion">تحويل الألوان</option>
                            <option value="histogram">رسم الهيستوجرام</option>
                        </select>
                    </div>
                    <textarea id="code-editor" class="w-full h-64 p-4 border border-gray-300 rounded font-mono text-sm" placeholder="اكتب كود Python هنا..."></textarea>
                    <button onclick="runCode()" class="mt-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">تشغيل الكود</button>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">النتيجة</h4>
                    <div id="code-output" class="w-full h-80 p-4 border border-gray-300 rounded bg-gray-50 font-mono text-sm overflow-y-auto">
                        النتيجة ستظهر هنا...
                    </div>
                </div>
            </div>
        `;
        openToolModal('محرر الكود التفاعلي', content);
        initCodeEditor();
    }

    function openConceptMap() {
        const content = `
            <div class="space-y-6">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-semibold">خريطة المفاهيم التفاعلية</h4>
                    <div class="space-x-2">
                        <button onclick="generateConceptMap()" class="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700">إنشاء خريطة جديدة</button>
                        <button onclick="resetConceptMap()" class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">إعادة تعيين</button>
                    </div>
                </div>
                <div id="concept-map-container" class="border border-gray-300 bg-white" style="width: 100%; height: 500px; position: relative;">
                    <div class="flex items-center justify-center h-full text-gray-500">
                        انقر على "إنشاء خريطة جديدة" لبدء إنشاء خريطة المفاهيم
                    </div>
                </div>
            </div>
        `;
        openToolModal('مولد الخرائط المفاهيمية', content);
        initConceptMap();
    }

    // === Implementation Functions ===

    function initImageProcessor() {
        try {
            const imageInput = document.getElementById('imageInput');
            if (!imageInput) {
                throw new Error('Image input element not found');
            }

            // Add drag and drop functionality
            const dropZone = imageInput.parentElement;

            // File input change handler
            imageInput.addEventListener('change', handleImageUpload);

            // Drag and drop handlers
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('border-cyan-400', 'bg-cyan-50');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('border-cyan-400', 'bg-cyan-50');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('border-cyan-400', 'bg-cyan-50');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageFile(files[0]);
                }
            });

            AppState.currentTool = {
                type: 'image-processor',
                originalImageData: null,
                cleanup: () => {
                    // Cleanup resources
                    if (AppState.currentTool && AppState.currentTool.originalImageData) {
                        AppState.currentTool.originalImageData = null;
                    }
                }
            };

        } catch (error) {
            ErrorHandler.handle(error, 'image-processor', 'فشل في تهيئة محاكي معالجة الصور');
        }
    }

    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            handleImageFile(file);
        }
    }

    function handleImageFile(file) {
        try {
            // Validate file
            if (!file.type.startsWith('image/')) {
                throw new Error('الملف المحدد ليس صورة صالحة');
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                throw new Error('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 10MB');
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                const img = new Image();

                img.onload = function() {
                    try {
                        processImageLoad(img);
                    } catch (error) {
                        ErrorHandler.handle(error, 'image-processor', 'فشل في معالجة الصورة');
                    }
                };

                img.onerror = function() {
                    ErrorHandler.handle(new Error('فشل في تحميل الصورة'), 'image-processor');
                };

                img.src = e.target.result;
            };

            reader.onerror = function() {
                ErrorHandler.handle(new Error('فشل في قراءة الملف'), 'image-processor');
            };

            reader.readAsDataURL(file);

        } catch (error) {
            ErrorHandler.handle(error, 'image-processor');
        }
    }

    function processImageLoad(img) {
        const originalCanvas = document.getElementById('originalCanvas');
        const processedCanvas = document.getElementById('processedCanvas');
        const originalContainer = document.getElementById('original-image-container');
        const processedContainer = document.getElementById('processed-image-container');
        const filterButtons = document.querySelectorAll('.filter-btn');

        if (!originalCanvas || !processedCanvas) {
            throw new Error('Canvas elements not found');
        }

        const ctx1 = originalCanvas.getContext('2d');
        const ctx2 = processedCanvas.getContext('2d');

        // Calculate optimal size (max 400px while maintaining aspect ratio)
        const maxSize = 400;
        let { width, height } = img;

        if (width > height) {
            if (width > maxSize) {
                height = (height * maxSize) / width;
                width = maxSize;
            }
        } else {
            if (height > maxSize) {
                width = (width * maxSize) / height;
                height = maxSize;
            }
        }

        // Set canvas dimensions
        originalCanvas.width = processedCanvas.width = width;
        originalCanvas.height = processedCanvas.height = height;

        // Draw images
        ctx1.drawImage(img, 0, 0, width, height);
        ctx2.drawImage(img, 0, 0, width, height);

        // Store original image data
        AppState.currentTool.originalImageData = ctx1.getImageData(0, 0, width, height);

        // Show containers and enable buttons
        if (originalContainer) originalContainer.classList.remove('hidden');
        if (processedContainer) processedContainer.classList.remove('hidden');

        filterButtons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('opacity-50', 'cursor-not-allowed');
        });

        // Update progress
        updateProgress('tools', 'image-processor');

        ModalManager.announceToScreenReader('تم تحميل الصورة بنجاح. يمكنك الآن تطبيق المرشحات.');
    }

    // --- دوال الأزرار التفاعلية ---

    async function generateSummary() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `لخص هذا المحتوى من مادة الرؤية الحاسوبية في 3 نقاط رئيسية باللغة العربية:\n\n"${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("ملخص الفصل:", text);
        }
    }

    async function extractKeyTerms() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `من هذا المحتوى، استخرج 3 مصطلحات أساسية في الرؤية الحاسوبية واشرح كل منها في جملة واحدة باللغة العربية.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "ARRAY",
                    items: {
                        type: "OBJECT",
                        properties: {
                            term: { type: "STRING" },
                            definition: { type: "STRING" }
                        }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const terms = JSON.parse(text);
            let html = '<h4 class="font-bold text-lg mb-2">مصطلحات أساسية:</h4><ul class="list-disc list-inside space-y-2">';
            terms.forEach(t => {
                html += `<li><strong>${t.term}:</strong> ${t.definition}</li>`;
            });
            html += '</ul>';
            document.getElementById('gemini-content-area').innerHTML = html;
        }
    }

    async function generateAnalogy() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `اشرح المفهوم الأساسي في هذا النص باستخدام مثال أو مقارنة بسيطة من الحياة اليومية لتبسيطه. النص: "${content}"`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            displayTextResponse("تبسيط بمثال:", text);
        }
    }

    async function generateQuiz() {
        const content = document.getElementById('chapter-text').innerText;
        const prompt = `أنشئ سؤال اختيار من متعدد واحد فقط (سؤال واحد، 3 خيارات، وإجابة واحدة صحيحة) بناءً على هذا المحتوى.`;
        const payload = {
            contents: [{ parts: [{ text: `النص: "${content}".\n\nالمهمة: ${prompt}` }] }],
            generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: "OBJECT",
                    properties: {
                        question: { type: "STRING" },
                        options: { type: "ARRAY", items: { type: "STRING" } },
                        answer: { type: "STRING" }
                    }
                }
            }
        };
        const response = await callGeminiApi(payload);
        const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
            const quiz = JSON.parse(text);
            let html = `<h4 class="font-bold text-lg mb-2">اختبار سريع:</h4><p class="mb-2">${quiz.question}</p><div class="space-y-2">`;
            quiz.options.forEach(opt => {
                html += `<label class="block"><input type="radio" name="quiz-opt" value="${opt}" class="ml-2">${opt}</label>`;
            });
            html += `</div><button onclick="alert('الإجابة الصحيحة هي: ${quiz.answer}')" class="mt-4 bg-gray-200 text-gray-800 py-1 px-3 rounded-lg text-sm">إظهار الإجابة</button>`;
            document.getElementById('gemini-content-area').innerHTML = html;

            // Update progress
            updateProgress('quizzes', 1);
        }
    }

    // === Enhanced Image Processing Functions ===

    function applyFilter(filterType) {
        try {
            const startTime = performance.now();
            const processedCanvas = document.getElementById('processedCanvas');
            const processingTimeElement = document.getElementById('processing-time');

            if (!processedCanvas || !AppState.currentTool || !AppState.currentTool.originalImageData) {
                throw new Error('الصورة غير متوفرة. يرجى تحميل صورة أولاً.');
            }

            const ctx = processedCanvas.getContext('2d');
            const originalData = AppState.currentTool.originalImageData;

            // Create a copy of the original image data
            const imageData = new ImageData(
                new Uint8ClampedArray(originalData.data),
                originalData.width,
                originalData.height
            );

            // Apply the selected filter
            switch(filterType) {
                case 'grayscale':
                    applyGrayscaleFilter(imageData);
                    break;
                case 'blur':
                    applyBlurFilter(imageData);
                    break;
                case 'edge':
                    applyEdgeDetectionFilter(imageData);
                    break;
                case 'brightness':
                    applyBrightnessFilter(imageData, 50);
                    break;
                case 'contrast':
                    applyContrastFilter(imageData, 1.5);
                    break;
                default:
                    throw new Error(`مرشح غير معروف: ${filterType}`);
            }

            // Apply the processed image data to canvas
            ctx.putImageData(imageData, 0, 0);

            // Show processing time
            const processingTime = (performance.now() - startTime).toFixed(2);
            if (processingTimeElement) {
                processingTimeElement.textContent = `وقت المعالجة: ${processingTime}ms`;
            }

            // Update progress
            updateProgress('tools', 'image-processor');

            // Announce to screen readers
            ModalManager.announceToScreenReader(`تم تطبيق مرشح ${getFilterName(filterType)} بنجاح`);

        } catch (error) {
            ErrorHandler.handle(error, 'image-processor');
        }
    }

    // Individual filter implementations for better performance and modularity

    function applyGrayscaleFilter(imageData) {
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            // Use luminance formula for better grayscale conversion
            const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
            data[i] = data[i + 1] = data[i + 2] = gray;
        }
    }

    function applyBlurFilter(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const output = new Uint8ClampedArray(data);

        // Simple box blur
        const radius = 2;
        for (let y = radius; y < height - radius; y++) {
            for (let x = radius; x < width - radius; x++) {
                let r = 0, g = 0, b = 0, count = 0;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const idx = ((y + dy) * width + (x + dx)) * 4;
                        r += data[idx];
                        g += data[idx + 1];
                        b += data[idx + 2];
                        count++;
                    }
                }

                const idx = (y * width + x) * 4;
                output[idx] = r / count;
                output[idx + 1] = g / count;
                output[idx + 2] = b / count;
            }
        }

        // Copy the blurred data back
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    function applyEdgeDetectionFilter(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const output = new Uint8ClampedArray(data.length);

        // Sobel edge detection
        const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
        const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                let pixelX = 0, pixelY = 0;

                for (let i = 0; i < 9; i++) {
                    const dx = (i % 3) - 1;
                    const dy = Math.floor(i / 3) - 1;
                    const idx = ((y + dy) * width + (x + dx)) * 4;

                    // Convert to grayscale first
                    const gray = data[idx] * 0.299 + data[idx + 1] * 0.587 + data[idx + 2] * 0.114;

                    pixelX += gray * sobelX[i];
                    pixelY += gray * sobelY[i];
                }

                const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
                const idx = (y * width + x) * 4;

                output[idx] = output[idx + 1] = output[idx + 2] = Math.min(255, magnitude);
                output[idx + 3] = data[idx + 3]; // Keep alpha
            }
        }

        // Copy the edge-detected data back
        for (let i = 0; i < data.length; i++) {
            data[i] = output[i];
        }
    }

    function applyBrightnessFilter(imageData, brightness) {
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, data[i] + brightness));
            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + brightness));
            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + brightness));
        }
    }

    function applyContrastFilter(imageData, contrast) {
        const data = imageData.data;
        const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255));

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, factor * (data[i] - 128) + 128));
            data[i + 1] = Math.min(255, Math.max(0, factor * (data[i + 1] - 128) + 128));
            data[i + 2] = Math.min(255, Math.max(0, factor * (data[i + 2] - 128) + 128));
        }
    }

    function getFilterName(filterType) {
        const names = {
            'grayscale': 'التحويل إلى الرمادي',
            'blur': 'التشويش',
            'edge': 'كشف الحواف',
            'brightness': 'زيادة السطوع',
            'contrast': 'زيادة التباين'
        };
        return names[filterType] || filterType;
    }

    function resetImage() {
        try {
            const processedCanvas = document.getElementById('processedCanvas');
            const processingTimeElement = document.getElementById('processing-time');

            if (!processedCanvas || !AppState.currentTool || !AppState.currentTool.originalImageData) {
                throw new Error('الصورة الأصلية غير متوفرة');
            }

            const ctx = processedCanvas.getContext('2d');
            ctx.putImageData(AppState.currentTool.originalImageData, 0, 0);

            if (processingTimeElement) {
                processingTimeElement.textContent = '';
            }

            ModalManager.announceToScreenReader('تم إعادة تعيين الصورة إلى حالتها الأصلية');

        } catch (error) {
            ErrorHandler.handle(error, 'image-processor');
        }
    }

    function downloadProcessedImage() {
        try {
            const processedCanvas = document.getElementById('processedCanvas');
            if (!processedCanvas) {
                throw new Error('الصورة المعالجة غير متوفرة');
            }

            // Create download link
            const link = document.createElement('a');
            link.download = `processed-image-${Date.now()}.png`;
            link.href = processedCanvas.toDataURL();

            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            ModalManager.announceToScreenReader('تم تحميل الصورة المعالجة بنجاح');

        } catch (error) {
            ErrorHandler.handle(error, 'image-processor', 'فشل في تحميل الصورة');
        }
    }


    // === 3D Visualization Functions ===

    function init3DVisualization() {
        if (typeof THREE === 'undefined') {
            document.getElementById('threejs-container').innerHTML = '<p class="text-red-500">مكتبة Three.js غير متوفرة</p>';
            return;
        }

        const container = document.getElementById('threejs-container');
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();

        renderer.setSize(container.offsetWidth, container.offsetHeight);
        container.appendChild(renderer.domElement);

        // Create a cube
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ color: 0x0891b2, wireframe: true });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);

        camera.position.z = 5;

        currentTool = { scene, camera, renderer, cube, animate: () => {
            requestAnimationFrame(currentTool.animate);
            renderer.render(scene, camera);
        }};

        currentTool.animate();
        updateProgress('tools', '3d-visualization');
    }

    function update3DScene() {
        if (!currentTool || !currentTool.cube) return;

        const rotX = document.getElementById('rotationX').value * Math.PI / 180;
        const rotY = document.getElementById('rotationY').value * Math.PI / 180;
        const rotZ = document.getElementById('rotationZ').value * Math.PI / 180;
        const zoom = document.getElementById('zoom').value;

        currentTool.cube.rotation.x = rotX;
        currentTool.cube.rotation.y = rotY;
        currentTool.cube.rotation.z = rotZ;
        currentTool.camera.position.z = zoom;
    }

    function reset3DScene() {
        document.getElementById('rotationX').value = 0;
        document.getElementById('rotationY').value = 0;
        document.getElementById('rotationZ').value = 0;
        document.getElementById('zoom').value = 5;
        update3DScene();
    }

    // === Algorithm Comparison Functions ===

    function initAlgorithmComparison() {
        updateAlgorithmComparison();
        updateProgress('tools', 'algorithm-comparison');
    }

    function updateAlgorithmComparison() {
        const algorithms = Array.from(document.querySelectorAll('input[type="checkbox"][value]'))
            .filter(cb => cb.checked && ['cnn', 'vit', 'svm', 'knn'].includes(cb.value))
            .map(cb => cb.value);

        const metrics = Array.from(document.querySelectorAll('input[type="checkbox"][value]'))
            .filter(cb => cb.checked && ['accuracy', 'speed', 'memory'].includes(cb.value))
            .map(cb => cb.value);

        if (algorithms.length === 0 || metrics.length === 0) return;

        // Sample data for demonstration
        const data = {
            cnn: { accuracy: 85, speed: 70, memory: 60 },
            vit: { accuracy: 90, speed: 50, memory: 40 },
            svm: { accuracy: 75, speed: 90, memory: 95 },
            knn: { accuracy: 70, speed: 95, memory: 98 }
        };

        const canvas = document.getElementById('comparisonChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Simple bar chart
        const barWidth = canvas.width / (algorithms.length * metrics.length + algorithms.length);
        let x = 20;

        algorithms.forEach((alg, algIndex) => {
            metrics.forEach((metric, metricIndex) => {
                const value = data[alg][metric];
                const barHeight = (value / 100) * (canvas.height - 40);

                ctx.fillStyle = `hsl(${algIndex * 60}, 70%, 50%)`;
                ctx.fillRect(x, canvas.height - barHeight - 20, barWidth - 5, barHeight);

                // Label
                ctx.fillStyle = 'black';
                ctx.font = '10px Arial';
                ctx.fillText(`${alg}-${metric}`, x, canvas.height - 5);

                x += barWidth;
            });
            x += 10;
        });
    }

    // === Progress Tracking Functions ===

    function initProgressTracker() {
        updateProgressDisplay();
        updateProgress('tools', 'progress-tracker');
    }

    function updateProgress(type, value) {
        if (!progressData[type]) progressData[type] = new Set();

        if (type === 'quizzes') {
            progressData[type] = (progressData[type] || 0) + value;
        } else if (type === 'tools') {
            progressData[type].add(value);
        } else if (type === 'chapters') {
            progressData[type].add(value);
        }

        localStorage.setItem('cvProgress', JSON.stringify(progressData, (key, value) => {
            if (value instanceof Set) return Array.from(value);
            return value;
        }));

        updateProgressDisplay();
    }

    function updateProgressDisplay() {
        const completedChapters = document.getElementById('completed-chapters');
        const progressBar = document.getElementById('progress-bar');
        const completedQuizzes = document.getElementById('completed-quizzes');
        const toolsUsed = document.getElementById('tools-used');

        if (completedChapters) {
            const chapters = progressData.chapters ? progressData.chapters.length || progressData.chapters.size : 0;
            completedChapters.textContent = `${chapters}/9`;
            if (progressBar) {
                progressBar.style.width = `${(chapters / 9) * 100}%`;
            }
        }

        if (completedQuizzes) {
            completedQuizzes.textContent = progressData.quizzes || 0;
        }

        if (toolsUsed) {
            const tools = progressData.tools ? progressData.tools.length || progressData.tools.size : 0;
            toolsUsed.textContent = `${tools}/6`;
        }

        updateAchievements();
    }

    function updateAchievements() {
        const achievementsList = document.getElementById('achievements-list');
        if (!achievementsList) return;

        const achievements = [];
        const chapters = progressData.chapters ? progressData.chapters.length || progressData.chapters.size : 0;
        const tools = progressData.tools ? progressData.tools.length || progressData.tools.size : 0;
        const quizzes = progressData.quizzes || 0;

        if (chapters >= 3) achievements.push('🎓 مستكشف المعرفة - أكمل 3 فصول');
        if (tools >= 3) achievements.push('🛠️ خبير الأدوات - استخدم 3 أدوات مختلفة');
        if (quizzes >= 5) achievements.push('🧠 عبقري الاختبارات - أكمل 5 اختبارات');
        if (chapters === 9) achievements.push('🏆 سيد الرؤية الحاسوبية - أكمل جميع الفصول');

        achievementsList.innerHTML = achievements.length > 0
            ? achievements.map(a => `<div class="bg-yellow-100 p-2 rounded">${a}</div>`).join('')
            : '<div class="text-gray-500">لا توجد إنجازات بعد</div>';
    }

    function resetProgress() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            progressData = {};
            localStorage.removeItem('cvProgress');
            updateProgressDisplay();
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        try {
            console.log("🚀 Initializing Computer Vision Educational Platform...");

            // Initialize application state
            AppState.init();

            // Initialize progress data from localStorage (legacy support)
            const stored = localStorage.getItem('cvProgress');
            if (stored && !AppState.progressData.chapters) {
                const parsed = JSON.parse(stored);
                AppState.progressData = {};
                Object.keys(parsed).forEach(key => {
                    if (Array.isArray(parsed[key])) {
                        AppState.progressData[key] = new Set(parsed[key]);
                    } else {
                        AppState.progressData[key] = parsed[key];
                    }
                });
            }

        const courseContent = [
            {
                title: "الفصل 1: تمثيل الصور ومعالجتها",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تكوين الصور:</strong> الكاميرات، المستشعرات.</li><li><strong>الهندسة:</strong> الإحداثيات المتجانسة وتحويلاتها.</li><li><strong>القياس الضوئي (Photometry):</strong> إضاءة الشعاع والصورة.</li><li><strong>فضاءات الألوان:</strong> RGB, HSV, YCbCr.</li><li><strong>العمليات الأساسية:</strong> الفلترة، تحديد العتبة (Thresholding)، والتجزئة البسيطة.</li></ul>`
            },
            {
                title: "الفصل 2: كشف واستخلاص الخصائص",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النقاط المهمة (Points of Interest):</strong> Harris, SIFT, SURF, ORB.</li><li><strong>الواصفات (Descriptors):</strong> وصف الخصائص والمطابقة بينها.</li><li><strong>حالة عملية:</strong> تتبع الكائنات (Object Tracking).</li></ul>`
            },
            {
                title: "الفصل 3: التعرف على الكائنات والرؤية الهندسية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>النماذج الهندسية:</strong> القطبية الثنائية (Epipolarity)، التحويل المتجانس (Homography).</li><li><strong>معايرة الكاميرا (Camera Calibration):</strong> وإعادة البناء ثلاثي الأبعاد.</li><li><strong>كشف وتتبع الكائنات:</strong> HOG, Viola-Jones.</li></ul>`
            },
            {
                title: "الفصل 4: CNNs للرؤية",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>أساسيات الشبكات العصبونية الالتفافية.</strong></li><li><strong>البنى الكلاسيكية:</strong> LeNet, AlexNet, VGG.</li><li><strong>مشاكل شائعة:</strong> التخصيص المفرط (Overfitting)، التنظيم (Regularization)، نقل المعرفة (Transfer Learning).</li></ul>`
            },
            {
                title: "الفصل 5: CNNs متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>بنى حديثة:</strong> ResNet, DenseNet, EfficientNet.</li><li><strong>شبكات التجزئة (Segmentation):</strong> U-Net, Mask R-CNN.</li><li><strong>كشف الكائنات (Object Detection):</strong> YOLO, Faster R-CNN, DETR.</li></ul>`
            },
            {
                title: "الفصل 6: محولات الرؤية (ViT)",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>مبدأ عمل محولات الرؤية المطبقة على الصور.</strong></li><li><strong>مقارنة بين CNN و ViT.</strong></li><li><strong>تطبيقات حديثة:</strong> التصنيف، التجزئة، والكشف.</li></ul>`
            },
            {
                title: "الفصل 7: الرؤية ثلاثية الأبعاد والفيديو",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>تقدير الحركة والتدفق البصري (Optical Flow).</strong></li><li><strong>الرؤية المجسمة (Stereo Vision).</strong></li><li><strong>تحليل الحركات في مقاطع الفيديو.</strong></li></ul>`
            },
            {
                title: "الفصل 8: الرؤية متعددة الوسائط والتعلم الذاتي",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>التعلم الذاتي المراقب (Self-supervised):</strong> SimCLR, MAE, MoCo.</li><li><strong>النماذج متعددة الوسائط (Multimodal):</strong> CLIP, BLIP-2.</li><li><strong>تطبيقات:</strong> البحث عن الصور بالنص، الإجابة على الأسئلة المرئية (VQA).</li></ul>`
            },
            {
                title: "الفصل 9: تطبيقات متقدمة",
                content: `<ul class='list-disc list-inside text-right space-y-2'><li><strong>الرؤية الطبية:</strong> تحليل الأشعة والصور الطبية الحيوية.</li><li><strong>القيادة الذاتية:</strong> كشف البيئة المحيطة.</li><li><strong>الواقع المعزز، القياسات الحيوية، والروبوتات.</strong></li></ul>`
            }
        ];

        const tabsContainer = document.getElementById('tabs-container');
        const contentContainer = document.getElementById('tab-content-container');

        if (!tabsContainer || !contentContainer) {
            console.error("Tab containers not found!");
            return;
        }

        function updateTabContent(item) {
            const buttonsHTML = `
                <div class="flex flex-wrap justify-center gap-3 mt-6 pt-4 border-t-2 border-gray-200">
                    <button onclick="generateSummary()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">✨ تلخيص</button>
                    <button onclick="generateQuiz()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🧠 إنشاء اختبار</button>
                    <button onclick="extractKeyTerms()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">🔑 مصطلحات أساسية</button>
                    <button onclick="generateAnalogy()" class="bg-cyan-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-cyan-700 transition text-sm">💡 تبسيط بمثال</button>
                </div>
            `;

            contentContainer.innerHTML = `
                <h3 class='text-2xl font-bold text-cyan-800 mb-4 text-right'>${item.title}</h3>
                <div class='text-gray-700 leading-relaxed' id='chapter-text'>${item.content}</div>
                ${buttonsHTML}
                <div id="gemini-content-area" class="mt-4 p-4 bg-gray-50 rounded-lg min-h-[50px] border border-gray-200"></div>
            `;
        }

        courseContent.forEach((item, index) => {
            const tabButton = document.createElement('button');
            tabButton.textContent = item.title;
            tabButton.className = 'tab-btn px-4 py-2 text-sm font-semibold border-2 border-gray-300 rounded-full transition hover:bg-cyan-500 hover:text-white hover:border-cyan-500 focus:outline-none';
            
            if (index === 0) {
                tabButton.classList.add('active');
                updateTabContent(item);
            }

            tabButton.addEventListener('click', () => {
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                tabButton.classList.add('active');
                updateTabContent(item);
            });

            tabsContainer.appendChild(tabButton);
        });

        // === Code Editor Functions ===

        function initCodeEditor() {
            updateProgress('tools', 'code-editor');
        }

        function loadCodeTemplate() {
            const template = document.getElementById('code-template').value;
            const editor = document.getElementById('code-editor');

            const templates = {
                'image-loading': `import cv2
import matplotlib.pyplot as plt

# تحميل الصورة
image = cv2.imread('image.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

# عرض الصورة
plt.imshow(image_rgb)
plt.title('الصورة الأصلية')
plt.axis('off')
plt.show()`,

                'edge-detection': `import cv2
import numpy as np

# تحميل الصورة وتحويلها إلى رمادي
image = cv2.imread('image.jpg')
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# تطبيق كشف الحواف باستخدام Canny
edges = cv2.Canny(gray, 100, 200)

# عرض النتيجة
cv2.imshow('الحواف', edges)
cv2.waitKey(0)
cv2.destroyAllWindows()`,

                'color-conversion': `import cv2

# تحميل الصورة
image = cv2.imread('image.jpg')

# تحويل من BGR إلى HSV
hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

# تحويل من BGR إلى RGB
rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

print("تم تحويل الصورة بنجاح!")`,

                'histogram': `import cv2
import matplotlib.pyplot as plt

# تحميل الصورة
image = cv2.imread('image.jpg')
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# حساب الهيستوجرام
hist = cv2.calcHist([gray], [0], None, [256], [0, 256])

# رسم الهيستوجرام
plt.plot(hist)
plt.title('هيستوجرام الصورة')
plt.xlabel('شدة البكسل')
plt.ylabel('عدد البكسلات')
plt.show()`
            };

            if (templates[template]) {
                editor.value = templates[template];
            }
        }

        function runCode() {
            const code = document.getElementById('code-editor').value;
            const output = document.getElementById('code-output');

            // Simulate code execution (in a real implementation, you'd send this to a backend)
            output.innerHTML = `<div class="text-green-600">تم تشغيل الكود بنجاح!</div>
<div class="mt-2 text-gray-600">ملاحظة: هذا محاكي للكود. في التطبيق الحقيقي، سيتم تشغيل الكود على الخادم.</div>
<div class="mt-2 bg-gray-100 p-2 rounded"><pre>${code}</pre></div>`;
        }

        // === Concept Map Functions ===

        function initConceptMap() {
            updateProgress('tools', 'concept-map');
        }

        async function generateConceptMap() {
            const container = document.getElementById('concept-map-container');
            container.innerHTML = '<div class="flex items-center justify-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-600"></div></div>';

            try {
                const prompt = "أنشئ خريطة مفاهيمية للرؤية الحاسوبية تتضمن المفاهيم الأساسية والعلاقات بينها";
                const payload = {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        responseMimeType: "application/json",
                        responseSchema: {
                            type: "OBJECT",
                            properties: {
                                nodes: {
                                    type: "ARRAY",
                                    items: {
                                        type: "OBJECT",
                                        properties: {
                                            id: { type: "STRING" },
                                            label: { type: "STRING" },
                                            x: { type: "NUMBER" },
                                            y: { type: "NUMBER" }
                                        }
                                    }
                                },
                                connections: {
                                    type: "ARRAY",
                                    items: {
                                        type: "OBJECT",
                                        properties: {
                                            from: { type: "STRING" },
                                            to: { type: "STRING" },
                                            label: { type: "STRING" }
                                        }
                                    }
                                }
                            }
                        }
                    }
                };

                const response = await callGeminiApi(payload);
                const text = response?.candidates?.[0]?.content?.parts?.[0]?.text;

                if (text) {
                    const mapData = JSON.parse(text);
                    renderConceptMap(mapData);
                } else {
                    throw new Error('No response from AI');
                }
            } catch (error) {
                console.error('Error generating concept map:', error);
                container.innerHTML = `
                    <div class="p-4">
                        <div class="text-red-500 mb-4">حدث خطأ في إنشاء الخريطة. إليك خريطة مفاهيمية أساسية:</div>
                        <div class="concept-map-demo">
                            <div class="concept-node" style="position: absolute; top: 50px; left: 200px; background: #0891b2; color: white; padding: 10px; border-radius: 8px;">الرؤية الحاسوبية</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 50px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">معالجة الصور</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 200px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">التعلم العميق</div>
                            <div class="concept-node" style="position: absolute; top: 150px; left: 350px; background: #06b6d4; color: white; padding: 8px; border-radius: 6px;">الرؤية ثلاثية الأبعاد</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 100px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">المرشحات</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 200px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">CNN</div>
                            <div class="concept-node" style="position: absolute; top: 250px; left: 300px; background: #67e8f9; color: black; padding: 6px; border-radius: 4px;">التجسيم</div>
                        </div>
                    </div>
                `;
            }
        }

        function renderConceptMap(mapData) {
            const container = document.getElementById('concept-map-container');
            let html = '<div class="relative w-full h-full">';

            // Render nodes
            mapData.nodes.forEach(node => {
                html += `<div class="concept-node absolute bg-cyan-600 text-white p-2 rounded shadow"
                         style="left: ${node.x}px; top: ${node.y}px;">${node.label}</div>`;
            });

            // Render connections (simplified as lines)
            mapData.connections.forEach(conn => {
                const fromNode = mapData.nodes.find(n => n.id === conn.from);
                const toNode = mapData.nodes.find(n => n.id === conn.to);
                if (fromNode && toNode) {
                    html += `<div class="connection-line absolute border-t-2 border-gray-400"
                             style="left: ${fromNode.x + 50}px; top: ${fromNode.y + 20}px;
                             width: ${Math.abs(toNode.x - fromNode.x)}px;"></div>`;
                }
            });

            html += '</div>';
            container.innerHTML = html;
        }

        function resetConceptMap() {
            const container = document.getElementById('concept-map-container');
            container.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">انقر على "إنشاء خريطة جديدة" لبدء إنشاء خريطة المفاهيم</div>';
        }

        // Make functions globally available
        window.openImageProcessor = openImageProcessor;
        window.open3DVisualization = open3DVisualization;
        window.openAlgorithmComparison = openAlgorithmComparison;
        window.openProgressTracker = openProgressTracker;
        window.openCodeEditor = openCodeEditor;
        window.openConceptMap = openConceptMap;
        window.closeToolModal = closeToolModal;
        window.applyFilter = applyFilter;
        window.resetImage = resetImage;
        window.update3DScene = update3DScene;
        window.reset3DScene = reset3DScene;
        window.updateAlgorithmComparison = updateAlgorithmComparison;
        window.resetProgress = resetProgress;
        window.loadCodeTemplate = loadCodeTemplate;
        window.runCode = runCode;
        window.generateConceptMap = generateConceptMap;
        window.resetConceptMap = resetConceptMap;

        const evaluationChartElement = document.getElementById('evaluationChart');
        if (evaluationChartElement) {
            const ctx = evaluationChartElement.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['امتحان فصلي (60%)', 'تقييم مستمر (40%)'],
                    datasets: [{
                        data: [60, 40],
                        backgroundColor: ['#0891b2', '#f97316'],
                        borderColor: '#f3f4f6',
                        borderWidth: 4,
                        hoverOffset: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: { 
                        legend: { position: 'bottom', labels: { padding: 20, font: { family: "'Cairo', sans-serif", size: 14 } } },
                        tooltip: { bodyFont: { family: "'Cairo', sans-serif" }, titleFont: { family: "'Cairo', sans-serif" } }
                    }
                }
            });
        }

        // Enhanced Mobile Menu with Accessibility
        const menuBtn = document.getElementById('menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        if (menuBtn && mobileMenu) {
            menuBtn.addEventListener('click', () => {
                const isExpanded = menuBtn.getAttribute('aria-expanded') === 'true';
                const newState = !isExpanded;

                menuBtn.setAttribute('aria-expanded', newState);
                mobileMenu.classList.toggle('hidden');

                // Update button icon
                const svg = menuBtn.querySelector('svg');
                if (svg) {
                    svg.style.transform = newState ? 'rotate(90deg)' : 'rotate(0deg)';
                }

                // Focus management
                if (newState) {
                    const firstLink = mobileMenu.querySelector('a');
                    if (firstLink) {
                        setTimeout(() => firstLink.focus(), 100);
                    }
                }
            });

            // Enhanced smooth scrolling with accessibility
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (!targetElement) {
                        console.warn(`Target element ${targetId} not found`);
                        return;
                    }

                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        menuBtn.setAttribute('aria-expanded', 'false');
                        const svg = menuBtn.querySelector('svg');
                        if (svg) svg.style.transform = 'rotate(0deg)';
                    }

                    // Smooth scroll with offset for sticky header
                    const headerHeight = document.querySelector('header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Focus the target for screen readers
                    setTimeout(() => {
                        targetElement.focus();
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 500);
                });
            });
        }

        // Keyboard navigation enhancements
        document.addEventListener('keydown', (e) => {
            // Global keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        // Focus search or first interactive element
                        const firstTool = document.querySelector('.tool-card button');
                        if (firstTool) firstTool.focus();
                        break;
                }
            }

            // Escape key to close modals
            if (e.key === 'Escape' && AppState.isModalOpen) {
                ModalManager.closeModal();
            }
        });

        // Performance: Intersection Observer for animations
        if ('IntersectionObserver' in window) {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe tool cards for entrance animations
            document.querySelectorAll('.tool-card').forEach(card => {
                observer.observe(card);
            });
        }

        // Performance monitoring
        if ('PerformanceObserver' in window) {
            try {
                const perfObserver = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                        if (entry.entryType === 'navigation') {
                            console.log(`Page load time: ${entry.loadEventEnd - entry.loadEventStart}ms`);
                        }
                    });
                });
                perfObserver.observe({ entryTypes: ['navigation'] });
            } catch (error) {
                console.warn('Performance monitoring not available:', error);
            }
        }

        // Service Worker registration for offline support (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                // Uncomment to enable service worker
                // navigator.serviceWorker.register('/sw.js')
                //     .then(registration => console.log('SW registered:', registration))
                //     .catch(error => console.log('SW registration failed:', error));
            });
        }

        console.log('🎉 Computer Vision Educational Platform initialized successfully!');
        console.log('📊 Performance optimizations active');
        console.log('♿ Accessibility features enabled');
        console.log('📱 Mobile responsiveness ready');
        console.log('🌐 RTL support active');

    } catch (error) {
        console.error('Failed to initialize application:', error);
        ErrorHandler.handle(error, 'initialization', 'فشل في تهيئة التطبيق. يرجى إعادة تحميل الصفحة.');
    }
});
    </script>
</body>
</html>
